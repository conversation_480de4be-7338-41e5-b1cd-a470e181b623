BEGIN;

CREATE TABLE alembic_version (
    version_num VARCHAR(32) NOT NULL, 
    CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num)
);

-- Running upgrade  -> fa6e690d1dc3

COMMENT ON COLUMN alerts.stboxf IS NULL;

DROP INDEX alerts_index_user_id_gateway_id;

DROP INDEX fki_alerts_fk_gateway_id;

DROP INDEX fki_alerts_fk_user_id;

ALTER TABLE alerts DROP CONSTRAINT alerts_power_unit_id_fkey;

ALTER TABLE alerts DROP CONSTRAINT alerts_fk_user_id;

<PERSON>TER TABLE alerts DROP CONSTRAINT alerts_fk_gateway_id;

<PERSON>TER TABLE alerts DROP CONSTRAINT alerts_fk_power_unit_id;

ALTER TABLE alerts ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE alerts ADD FOREIGN KEY(power_unit_id) REFERENCES public.power_units (id) ON DELETE CASCADE;

ALTER TABLE alerts DROP COLUMN gateway_id;

ALTER TABLE alerts_custom ALTER COLUMN want_sms DROP NOT NULL;

ALTER TABLE alerts_custom ALTER COLUMN want_email DROP NOT NULL;

ALTER TABLE alerts_custom DROP CONSTRAINT alerts_custom_fk_hour;

ALTER TABLE alerts_custom DROP CONSTRAINT alerts_custom_hour_ending_fkey;

ALTER TABLE alerts_custom DROP CONSTRAINT alerts_custom_fk_customer_id;

ALTER TABLE alerts_custom ADD FOREIGN KEY(customer_id) REFERENCES public.customers (id);

ALTER TABLE alerts_custom ADD FOREIGN KEY(hour_ending) REFERENCES public.hours (hour_ending);

ALTER TABLE alerts_custom ADD FOREIGN KEY(time_zone_id) REFERENCES public.time_zones (id);

ALTER TABLE alerts_custom_days_rel DROP CONSTRAINT alerts_custom_days_rel_day_fkey;

ALTER TABLE alerts_custom_days_rel DROP CONSTRAINT alerts_custom_days_rel_alerts_custom_id_fkey;

ALTER TABLE alerts_custom_days_rel ADD FOREIGN KEY(day) REFERENCES public.days (day);

ALTER TABLE alerts_custom_days_rel ADD FOREIGN KEY(alerts_custom_id) REFERENCES public.alerts_custom (id) ON DELETE CASCADE;

ALTER TABLE alerts_custom_images_rel DROP CONSTRAINT alerts_custom_images_rel_image_id_fkey;

ALTER TABLE alerts_custom_images_rel DROP CONSTRAINT alerts_custom_images_rel_alerts_custom_id_fkey;

ALTER TABLE alerts_custom_images_rel ADD FOREIGN KEY(alerts_custom_id) REFERENCES public.alerts_custom (id) ON DELETE CASCADE;

ALTER TABLE alerts_custom_images_rel ADD FOREIGN KEY(image_id) REFERENCES public.images (id) ON DELETE CASCADE;

ALTER TABLE alerts_custom_months_rel DROP CONSTRAINT alerts_custom_months_rel_month_fkey1;

ALTER TABLE alerts_custom_months_rel DROP CONSTRAINT alerts_custom_months_rel_alerts_custom_id_fkey;

ALTER TABLE alerts_custom_months_rel ADD FOREIGN KEY(alerts_custom_id) REFERENCES public.alerts_custom (id) ON DELETE CASCADE;

ALTER TABLE alerts_custom_months_rel ADD FOREIGN KEY(month) REFERENCES public.months (month);

ALTER TABLE alerts_custom_structure_rel DROP CONSTRAINT alerts_custom_structure_rel_alerts_custom_id_fkey;

ALTER TABLE alerts_custom_structure_rel DROP CONSTRAINT alerts_custom_structure_rel_structure_id_fkey;

ALTER TABLE alerts_custom_structure_rel ADD FOREIGN KEY(alerts_custom_id) REFERENCES public.alerts_custom (id) ON DELETE CASCADE;

ALTER TABLE alerts_custom_structure_rel ADD FOREIGN KEY(structure_id) REFERENCES public.structures (id) ON DELETE CASCADE;

ALTER TABLE alerts_custom_user_rel DROP CONSTRAINT alerts_custom_user_rel_alerts_custom_id_fkey;

ALTER TABLE alerts_custom_user_rel ADD FOREIGN KEY(alerts_custom_id) REFERENCES public.alerts_custom (id) ON DELETE CASCADE;

ALTER TABLE alerts_custom_user_rel ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

DROP INDEX alerts_sent_index_timestamp_utc_gateway;

COMMENT ON TABLE alerts_sent IS NULL;

ALTER TABLE alerts_sent_maint DROP CONSTRAINT alerts_sent_maint_timestamp_utc_customer_id_email_type_key;

ALTER TABLE alerts_sent_maint DROP CONSTRAINT alerts_sent_maint_email_type_id_fkey;

ALTER TABLE alerts_sent_maint DROP CONSTRAINT alerts_sent_maint_fk_customer_id;

ALTER TABLE alerts_sent_maint ADD FOREIGN KEY(customer_id) REFERENCES public.customers (id);

ALTER TABLE alerts_sent_maint ADD FOREIGN KEY(email_type_id) REFERENCES public.alerts_sent_maint_email_types (id);

ALTER TABLE alerts_sent_maint DROP COLUMN customer;

ALTER TABLE alerts_sent_maint DROP COLUMN email_type;

ALTER TABLE alerts_sent_maint_users ALTER COLUMN dev_test_prd DROP NOT NULL;

ALTER TABLE alerts_sent_maint_users DROP CONSTRAINT alerts_sent_maint_users_alerts_sent_maint_id_fkey;

ALTER TABLE alerts_sent_maint_users ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE alerts_sent_maint_users ADD FOREIGN KEY(alerts_sent_maint_id) REFERENCES public.alerts_sent_maint (id);

ALTER TABLE alerts_sent_maint_users DROP COLUMN msg_type;

COMMENT ON TABLE alerts_sent_other IS NULL;

COMMENT ON COLUMN alerts_sent_users.msg_type IS NULL;

ALTER TABLE alerts_sent_users DROP CONSTRAINT alerts_sent_users_alerts_sent_id_fkey;

ALTER TABLE alerts_sent_users ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE alerts_sent_users ADD FOREIGN KEY(alerts_sent_id) REFERENCES public.alerts_sent (id);

ALTER TABLE alerts_sent_wait_okay ALTER COLUMN alert_type_id DROP NOT NULL;

ALTER TABLE alerts_sent_wait_okay DROP CONSTRAINT alerts_sent_wait_okay_power_unit_id_fkey;

ALTER TABLE alerts_sent_wait_okay DROP CONSTRAINT alerts_sent_wait_okay_alert_type_id_fkey;

ALTER TABLE alerts_sent_wait_okay ADD FOREIGN KEY(alert_type_id) REFERENCES public.alerts_types (id);

ALTER TABLE alerts_sent_wait_okay ADD FOREIGN KEY(power_unit_id) REFERENCES public.power_units (id);

ALTER TABLE alerts_types DROP COLUMN abbrev;

ALTER TABLE application_types ADD FOREIGN KEY(unit_type_id) REFERENCES public.unit_types (id);

ALTER TABLE application_upload_files DROP CONSTRAINT application_upload_files_application_id_fkey;

ALTER TABLE application_upload_files ADD FOREIGN KEY(application_id) REFERENCES public.applications (id);

ALTER TABLE applications DROP CONSTRAINT applications_customer_id_fkey;

ALTER TABLE applications DROP CONSTRAINT applications_user_id_fkey;

ALTER TABLE applications DROP CONSTRAINT applications_application_type_id_fkey;

ALTER TABLE applications DROP CONSTRAINT applications_province_id_fkey;

ALTER TABLE applications ADD FOREIGN KEY(country_id) REFERENCES public.countries (id);

ALTER TABLE applications ADD FOREIGN KEY(province_id) REFERENCES public.provinces (id);

ALTER TABLE applications ADD FOREIGN KEY(user_id) REFERENCES public.users (id);

ALTER TABLE applications ADD FOREIGN KEY(application_type_id) REFERENCES public.application_types (id);

ALTER TABLE applications ADD FOREIGN KEY(customer_id) REFERENCES public.customers (id);

ALTER TABLE bom_base_powerunit_part_rel DROP CONSTRAINT bom_base_powerunit_part_rel_finished_good_id_fkey;

ALTER TABLE bom_base_powerunit_part_rel DROP CONSTRAINT bom_base_powerunit_part_rel_part_id_fkey;

ALTER TABLE bom_base_powerunit_part_rel ADD FOREIGN KEY(finished_good_id) REFERENCES public.bom_base_powerunit (id) ON DELETE CASCADE;

ALTER TABLE bom_base_powerunit_part_rel ADD FOREIGN KEY(part_id) REFERENCES public.parts (id) ON DELETE CASCADE;

ALTER TABLE bom_base_powerunit_power_unit_type_rel DROP CONSTRAINT bom_base_powerunit_power_unit_type_rel_finished_good_id_fkey;

ALTER TABLE bom_base_powerunit_power_unit_type_rel ADD FOREIGN KEY(finished_good_id) REFERENCES public.bom_base_powerunit (id);

ALTER TABLE bom_base_powerunit_power_unit_type_rel ADD FOREIGN KEY(power_unit_type_id) REFERENCES public.power_unit_types (id);

ALTER TABLE bom_dgas_model_type_rel DROP CONSTRAINT bom_dgas_model_type_rel_finished_good_id_fkey;

ALTER TABLE bom_dgas_model_type_rel ADD FOREIGN KEY(finished_good_id) REFERENCES public.bom_dgas (id);

ALTER TABLE bom_dgas_model_type_rel ADD FOREIGN KEY(model_type_id) REFERENCES public.model_types (id);

ALTER TABLE bom_dgas_part_rel DROP CONSTRAINT bom_dgas_part_rel_part_id_fkey;

ALTER TABLE bom_dgas_part_rel DROP CONSTRAINT bom_dgas_part_rel_finished_good_id_fkey;

ALTER TABLE bom_dgas_part_rel ADD FOREIGN KEY(finished_good_id) REFERENCES public.bom_dgas (id);

ALTER TABLE bom_dgas_part_rel ADD FOREIGN KEY(part_id) REFERENCES public.parts (id);

ALTER TABLE bom_powerunit_part_rel DROP CONSTRAINT bom_powerunit_part_rel_part_id_fkey;

ALTER TABLE bom_powerunit_part_rel DROP CONSTRAINT bom_powerunit_part_rel_finished_good_id_fkey;

ALTER TABLE bom_powerunit_part_rel ADD FOREIGN KEY(part_id) REFERENCES public.parts (id) ON DELETE CASCADE;

ALTER TABLE bom_powerunit_part_rel ADD FOREIGN KEY(finished_good_id) REFERENCES public.bom_powerunit (id) ON DELETE CASCADE;

ALTER TABLE bom_powerunit_power_unit_type_rel DROP CONSTRAINT bom_powerunit_power_unit_type_power_unit_type_id_fkey;

ALTER TABLE bom_powerunit_power_unit_type_rel DROP CONSTRAINT bom_powerunit_power_unit_type_rel_finished_good_id_fkey;

ALTER TABLE bom_powerunit_power_unit_type_rel ADD FOREIGN KEY(finished_good_id) REFERENCES public.bom_powerunit (id);

ALTER TABLE bom_powerunit_power_unit_type_rel ADD FOREIGN KEY(power_unit_type_id) REFERENCES public.power_unit_types (id);

ALTER TABLE bom_pricing_model_type_rel DROP CONSTRAINT bom_pricing_model_type_rel_finished_good_id_fkey;

ALTER TABLE bom_pricing_model_type_rel DROP CONSTRAINT bom_pricing_model_type_rel_model_type_id_fkey;

ALTER TABLE bom_pricing_model_type_rel ADD FOREIGN KEY(model_type_id) REFERENCES public.model_types (id) ON DELETE CASCADE;

ALTER TABLE bom_pricing_model_type_rel ADD FOREIGN KEY(finished_good_id) REFERENCES public.bom_pricing (id);

ALTER TABLE bom_pricing_part_rel DROP CONSTRAINT bom_pricing_part_rel_finished_good_id_fkey;

ALTER TABLE bom_pricing_part_rel DROP CONSTRAINT bom_pricing_part_rel_part_id_fkey;

ALTER TABLE bom_pricing_part_rel ADD FOREIGN KEY(part_id) REFERENCES public.parts (id) ON DELETE CASCADE;

ALTER TABLE bom_pricing_part_rel ADD FOREIGN KEY(finished_good_id) REFERENCES public.bom_pricing (id) ON DELETE CASCADE;

ALTER TABLE bom_pump_top_model_type_rel DROP CONSTRAINT bom_pump_top_model_type_rel_finished_good_id_fkey;

ALTER TABLE bom_pump_top_model_type_rel ADD FOREIGN KEY(finished_good_id) REFERENCES public.bom_pump_top (id);

ALTER TABLE bom_pump_top_model_type_rel ADD FOREIGN KEY(model_type_id) REFERENCES public.model_types (id);

ALTER TABLE bom_pump_top_part_rel DROP CONSTRAINT bom_pump_top_part_rel_finished_good_id_fkey;

ALTER TABLE bom_pump_top_part_rel DROP CONSTRAINT bom_pump_top_part_rel_part_id_fkey;

ALTER TABLE bom_pump_top_part_rel ADD FOREIGN KEY(part_id) REFERENCES public.parts (id);

ALTER TABLE bom_pump_top_part_rel ADD FOREIGN KEY(finished_good_id) REFERENCES public.bom_pump_top (id);

ALTER TABLE bom_structure_model_type_rel DROP CONSTRAINT bom_structure_model_type_rel_finished_good_id_fkey;

ALTER TABLE bom_structure_model_type_rel ADD FOREIGN KEY(model_type_id) REFERENCES public.model_types (id) ON DELETE CASCADE;

ALTER TABLE bom_structure_model_type_rel ADD FOREIGN KEY(finished_good_id) REFERENCES public.bom_structure (id) ON DELETE CASCADE;

ALTER TABLE bom_structure_part_rel DROP CONSTRAINT bom_structure_part_rel_part_id_fkey;

ALTER TABLE bom_structure_part_rel DROP CONSTRAINT bom_structure_part_rel_finished_good_id_fkey;

ALTER TABLE bom_structure_part_rel ADD FOREIGN KEY(finished_good_id) REFERENCES public.bom_structure (id) ON DELETE CASCADE;

ALTER TABLE bom_structure_part_rel ADD FOREIGN KEY(part_id) REFERENCES public.parts (id) ON DELETE CASCADE;

ALTER TABLE calculators DROP CONSTRAINT calculators_power_unit_type_id_fkey;

ALTER TABLE calculators DROP CONSTRAINT calculators_model_type_id_fkey;

ALTER TABLE calculators ADD FOREIGN KEY(model_type_id) REFERENCES public.model_types (id);

ALTER TABLE calculators ADD FOREIGN KEY(power_unit_type_id) REFERENCES public.power_unit_types (id);

ALTER TABLE calories DROP CONSTRAINT calories_calorie_type_id_fkey;

ALTER TABLE calories DROP CONSTRAINT calories_user_id_fkey;

ALTER TABLE calories ADD FOREIGN KEY(calorie_type_id) REFERENCES public.calorie_types (id);

ALTER TABLE calories ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE calories DROP COLUMN description;

ALTER TABLE career_applications DROP CONSTRAINT career_applications_fk_user_id;

ALTER TABLE career_applications ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE career_applications_files_rel DROP CONSTRAINT career_application_files_career_application_id_fkey;

ALTER TABLE career_applications_files_rel DROP CONSTRAINT career_applications_files_rel_career_application_id_fkey;

ALTER TABLE career_applications_files_rel DROP CONSTRAINT career_application_files_career_file_id_fkey;

ALTER TABLE career_applications_files_rel DROP CONSTRAINT career_applications_files_rel_career_file_id_fkey;

ALTER TABLE career_applications_files_rel ADD FOREIGN KEY(career_file_id) REFERENCES public.career_files (id) ON DELETE CASCADE;

ALTER TABLE career_applications_files_rel ADD FOREIGN KEY(career_application_id) REFERENCES public.career_applications (id) ON DELETE CASCADE;

ALTER TABLE career_files DROP CONSTRAINT career_files_fk_user_id;

ALTER TABLE career_files ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE cities DROP CONSTRAINT cities_county_id_fkey;

ALTER TABLE cities DROP CONSTRAINT cities_province_id_fkey;

ALTER TABLE cities ADD FOREIGN KEY(county_id) REFERENCES public.counties (id);

ALTER TABLE cities ADD FOREIGN KEY(province_id) REFERENCES public.provinces (id);

DROP INDEX compression_images_index_cluster_id_ml_version;

ALTER TABLE compression_images DROP CONSTRAINT compression_images_fk_pattern_id;

ALTER TABLE compression_images ADD FOREIGN KEY(pattern_id) REFERENCES public.compression_patterns (id);

ALTER TABLE contact_form DROP CONSTRAINT contact_form_user_id_fkey;

ALTER TABLE contact_form ADD FOREIGN KEY(user_id) REFERENCES public.users (id);

ALTER TABLE counties DROP CONSTRAINT counties_province_id_fkey;

ALTER TABLE counties ADD FOREIGN KEY(province_id) REFERENCES public.provinces (id);

COMMENT ON COLUMN currencies.fx_rate_cad_per IS NULL;

ALTER TABLE currencies ADD FOREIGN KEY(country_id) REFERENCES public.countries (id);

COMMENT ON COLUMN currencies_rates.id IS NULL;

COMMENT ON COLUMN currencies_rates.currency_id IS NULL;

COMMENT ON COLUMN currencies_rates.rate_date IS NULL;

COMMENT ON COLUMN currencies_rates.fx_rate_cad_per IS NULL;

COMMENT ON COLUMN currencies_rates.source IS NULL;

DROP INDEX idx_currencies_rates_currency_date;

DROP INDEX idx_currencies_rates_currency_id;

DROP INDEX idx_currencies_rates_rate_date;

ALTER TABLE currencies_rates DROP CONSTRAINT fk_currencies_rates_currency_id;

ALTER TABLE currencies_rates ADD FOREIGN KEY(currency_id) REFERENCES public.currencies (id);

COMMENT ON TABLE currencies_rates IS NULL;

ALTER TABLE cust_sub_groups DROP CONSTRAINT cust_sub_groups_customer_id_fkey;

ALTER TABLE cust_sub_groups ADD FOREIGN KEY(customer_id) REFERENCES public.customers (id);

ALTER TABLE customers DROP CONSTRAINT customers_province_id_fkey;

ALTER TABLE customers DROP CONSTRAINT customers_country_id_fkey;

ALTER TABLE customers ADD FOREIGN KEY(province_id) REFERENCES public.provinces (id);

ALTER TABLE customers ADD CONSTRAINT customers_accounting_contact_id_fkey FOREIGN KEY(accounting_contact_id) REFERENCES public.users (id) DEFERRABLE INITIALLY DEFERRED;

ALTER TABLE customers ADD FOREIGN KEY(country_id) REFERENCES public.countries (id);

DROP INDEX idx_cycle_count_items_count;

DROP INDEX idx_cycle_count_items_location;

DROP INDEX idx_cycle_count_items_part;

ALTER TABLE cycle_count_items DROP CONSTRAINT cycle_count_items_counted_by_id_fkey;

ALTER TABLE cycle_count_items DROP CONSTRAINT cycle_count_items_cycle_count_id_fkey;

ALTER TABLE cycle_count_items DROP CONSTRAINT cycle_count_items_part_id_fkey;

ALTER TABLE cycle_count_items DROP CONSTRAINT cycle_count_items_location_id_fkey;

ALTER TABLE cycle_count_items DROP CONSTRAINT cycle_count_items_adjustment_movement_id_fkey;

ALTER TABLE cycle_count_items ADD FOREIGN KEY(adjustment_movement_id) REFERENCES public.inventory_movements (id);

ALTER TABLE cycle_count_items ADD FOREIGN KEY(part_id) REFERENCES public.parts (id);

ALTER TABLE cycle_count_items ADD FOREIGN KEY(cycle_count_id) REFERENCES public.cycle_counts (id);

ALTER TABLE cycle_count_items ADD FOREIGN KEY(location_id) REFERENCES public.warehouse_locations (id);

ALTER TABLE cycle_count_items ADD FOREIGN KEY(counted_by_id) REFERENCES public.users (id);

ALTER TABLE cycle_counts DROP CONSTRAINT cycle_counts_count_number_key;

DROP INDEX idx_cycle_count_number;

DROP INDEX idx_cycle_count_scheduled;

DROP INDEX idx_cycle_count_status;

DROP INDEX idx_cycle_count_warehouse;

CREATE UNIQUE INDEX ix_public_cycle_counts_count_number ON cycle_counts (count_number);

ALTER TABLE cycle_counts DROP CONSTRAINT cycle_counts_assigned_to_id_fkey;

ALTER TABLE cycle_counts DROP CONSTRAINT cycle_counts_warehouse_id_fkey;

ALTER TABLE cycle_counts DROP CONSTRAINT cycle_counts_created_by_id_fkey;

ALTER TABLE cycle_counts ADD FOREIGN KEY(warehouse_id) REFERENCES public.warehouses (id);

ALTER TABLE cycle_counts ADD FOREIGN KEY(assigned_to_id) REFERENCES public.users (id) ON DELETE SET NULL;

ALTER TABLE cycle_counts ADD FOREIGN KEY(created_by_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE days_of_week ALTER COLUMN id TYPE SMALLINT;

ALTER TABLE days_of_week ADD UNIQUE (name);

ALTER TABLE error_logs DROP CONSTRAINT error_logs_resolved_by_user_id_fkey;

ALTER TABLE error_logs DROP CONSTRAINT error_logs_user_id_fkey;

ALTER TABLE error_logs ADD FOREIGN KEY(resolved_by_user_id) REFERENCES public.users (id) ON DELETE SET NULL;

ALTER TABLE error_logs ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE SET NULL;

ALTER TABLE flask_dance_oauth DROP CONSTRAINT flask_dance_oauth_user_id_fkey;

ALTER TABLE flask_dance_oauth ADD FOREIGN KEY(user_id) REFERENCES public.users (id);

COMMENT ON COLUMN geocoding_cache.lat_rounded IS NULL;

COMMENT ON COLUMN geocoding_cache.lon_rounded IS NULL;

COMMENT ON COLUMN geocoding_cache.country_id IS NULL;

COMMENT ON COLUMN geocoding_cache.province_id IS NULL;

COMMENT ON COLUMN geocoding_cache.locality IS NULL;

COMMENT ON COLUMN geocoding_cache.confidence_score IS NULL;

COMMENT ON COLUMN geocoding_cache.data_source IS NULL;

COMMENT ON COLUMN geocoding_cache.created_at IS NULL;

COMMENT ON COLUMN geocoding_cache.updated_at IS NULL;

DROP INDEX idx_geocoding_cache_coordinates;

DROP INDEX idx_geocoding_cache_country_id;

DROP INDEX idx_geocoding_cache_created_at;

DROP INDEX idx_geocoding_cache_province_id;

ALTER TABLE geocoding_cache DROP CONSTRAINT geocoding_cache_country_id_fkey;

ALTER TABLE geocoding_cache DROP CONSTRAINT geocoding_cache_province_id_fkey;

ALTER TABLE geocoding_cache ADD FOREIGN KEY(country_id) REFERENCES public.countries (id);

ALTER TABLE geocoding_cache ADD FOREIGN KEY(province_id) REFERENCES public.provinces (id);

COMMENT ON TABLE geocoding_cache IS NULL;

ALTER TABLE gw DROP CONSTRAINT gw_id_unique;

DROP INDEX gw_index_gateway_structure_id_power_unit_id;

ALTER TABLE gw DROP CONSTRAINT gw_power_unit_id_fkey;

ALTER TABLE gw DROP CONSTRAINT gw_fk_structure_id;

ALTER TABLE gw DROP CONSTRAINT gw_fk_gateway_type_id;

ALTER TABLE gw ADD FOREIGN KEY(structure_id) REFERENCES public.structures (id);

ALTER TABLE gw ADD FOREIGN KEY(power_unit_id) REFERENCES public.power_units (id);

ALTER TABLE gw ADD FOREIGN KEY(gateway_type_id) REFERENCES public.gateway_types (id);

ALTER TABLE gw_info ALTER COLUMN timestamp_utc_last_reported SET NOT NULL;

ALTER TABLE gw_info ALTER COLUMN has_slave DROP NOT NULL;

ALTER TABLE gw_info DROP CONSTRAINT gw_info_gateway_id_fkey;

ALTER TABLE gw_info ADD FOREIGN KEY(gateway_id) REFERENCES public.gw (id);

ALTER TABLE gw_info DROP COLUMN gw_type_reported;

ALTER TABLE gw_not_connected_dont_worry DROP CONSTRAINT gw_not_connected_dont_worry_gateway_id_fkey;

ALTER TABLE gw_not_connected_dont_worry ADD FOREIGN KEY(gateway_id) REFERENCES public.gw (id) ON DELETE CASCADE;

ALTER TABLE gw_not_connected_dont_worry ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE gw_tested_cellular ALTER COLUMN timestamp_utc TYPE TIMESTAMP WITHOUT TIME ZONE;

ALTER TABLE gw_tested_cellular DROP CONSTRAINT gw_tested_cellular_gateway_id_fkey;

ALTER TABLE gw_tested_cellular DROP CONSTRAINT gw_tested_cellular_network_id_fkey;

ALTER TABLE gw_tested_cellular ADD FOREIGN KEY(network_id) REFERENCES public.gw_cell_networks (id) ON DELETE CASCADE;

ALTER TABLE gw_tested_cellular ADD FOREIGN KEY(gateway_id) REFERENCES public.gw (id) ON DELETE CASCADE;

ALTER TABLE gw_tested_cellular ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE hours ALTER COLUMN hour_ending DROP NOT NULL;

ALTER TABLE inventory_ledger DROP CONSTRAINT inventory_ledger_warehouse_id_fkey;

ALTER TABLE inventory_ledger DROP CONSTRAINT inventory_ledger_part_id_fkey;

ALTER TABLE inventory_ledger DROP CONSTRAINT inventory_ledger_reversal_of_id_fkey;

ALTER TABLE inventory_ledger DROP CONSTRAINT inventory_ledger_created_by_id_fkey;

ALTER TABLE inventory_ledger ADD FOREIGN KEY(warehouse_id) REFERENCES public.warehouses (id) ON DELETE RESTRICT;

ALTER TABLE inventory_ledger ADD FOREIGN KEY(part_id) REFERENCES public.parts (id) ON DELETE RESTRICT;

ALTER TABLE inventory_ledger ADD FOREIGN KEY(created_by_id) REFERENCES public.users (id);

ALTER TABLE inventory_ledger ADD FOREIGN KEY(reversal_of_id) REFERENCES public.inventory_ledger (id);

DROP INDEX idx_movement_number;

DROP INDEX idx_movement_reference;

DROP INDEX idx_movement_type;

ALTER TABLE inventory_movements DROP CONSTRAINT inventory_movements_movement_number_key;

CREATE UNIQUE INDEX ix_public_inventory_movements_movement_number ON inventory_movements (movement_number);

ALTER TABLE inventory_movements DROP CONSTRAINT inventory_movements_to_warehouse_id_fkey;

ALTER TABLE inventory_movements DROP CONSTRAINT inventory_movements_part_id_fkey;

ALTER TABLE inventory_movements DROP CONSTRAINT inventory_movements_from_warehouse_id_fkey;

ALTER TABLE inventory_movements DROP CONSTRAINT inventory_movements_reversal_movement_id_fkey;

ALTER TABLE inventory_movements DROP CONSTRAINT inventory_movements_to_location_id_fkey;

ALTER TABLE inventory_movements DROP CONSTRAINT inventory_movements_from_location_id_fkey;

ALTER TABLE inventory_movements DROP CONSTRAINT inventory_movements_work_order_part_id_fkey;

ALTER TABLE inventory_movements DROP CONSTRAINT inventory_movements_created_by_id_fkey;

ALTER TABLE inventory_movements ADD FOREIGN KEY(reversal_movement_id) REFERENCES public.inventory_movements (id);

ALTER TABLE inventory_movements ADD FOREIGN KEY(from_warehouse_id) REFERENCES public.warehouses (id);

ALTER TABLE inventory_movements ADD FOREIGN KEY(part_id) REFERENCES public.parts (id) ON DELETE RESTRICT;

ALTER TABLE inventory_movements ADD FOREIGN KEY(to_location_id) REFERENCES public.warehouse_locations (id);

ALTER TABLE inventory_movements ADD FOREIGN KEY(to_warehouse_id) REFERENCES public.warehouses (id);

ALTER TABLE inventory_movements ADD FOREIGN KEY(from_location_id) REFERENCES public.warehouse_locations (id);

ALTER TABLE inventory_movements ADD FOREIGN KEY(created_by_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE inventory_movements DROP COLUMN work_order_part_id;

DROP INDEX idx_reservation_number;

DROP INDEX idx_reservation_part_warehouse;

ALTER TABLE inventory_reservations DROP CONSTRAINT inventory_reservations_reservation_number_key;

CREATE UNIQUE INDEX ix_public_inventory_reservations_reservation_number ON inventory_reservations (reservation_number);

ALTER TABLE inventory_reservations DROP CONSTRAINT inventory_reservations_created_by_id_fkey;

ALTER TABLE inventory_reservations DROP CONSTRAINT inventory_reservations_work_order_part_id_fkey;

ALTER TABLE inventory_reservations DROP CONSTRAINT inventory_reservations_cancelled_by_id_fkey;

ALTER TABLE inventory_reservations DROP CONSTRAINT inventory_reservations_warehouse_to_id_fkey;

ALTER TABLE inventory_reservations DROP CONSTRAINT inventory_reservations_warehouse_id_fkey;

ALTER TABLE inventory_reservations DROP CONSTRAINT inventory_reservations_part_id_fkey;

ALTER TABLE inventory_reservations ADD FOREIGN KEY(warehouse_to_id) REFERENCES public.warehouses (id);

ALTER TABLE inventory_reservations ADD FOREIGN KEY(warehouse_id) REFERENCES public.warehouses (id);

ALTER TABLE inventory_reservations ADD FOREIGN KEY(created_by_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE inventory_reservations ADD FOREIGN KEY(part_id) REFERENCES public.parts (id) ON DELETE RESTRICT;

ALTER TABLE inventory_reservations ADD FOREIGN KEY(cancelled_by_id) REFERENCES public.users (id) ON DELETE SET NULL;

ALTER TABLE inventory_reservations DROP COLUMN work_order_part_id;

ALTER TABLE maintenance ALTER COLUMN maintenance_type_id SET NOT NULL;

ALTER TABLE maintenance DROP CONSTRAINT maintenance_power_unit_id_fkey;

ALTER TABLE maintenance DROP CONSTRAINT maintenance_maintenance_type_id_fkey;

ALTER TABLE maintenance DROP CONSTRAINT maintenance_structure_id_fkey;

ALTER TABLE maintenance ADD FOREIGN KEY(user_id) REFERENCES public.users (id);

ALTER TABLE maintenance ADD FOREIGN KEY(structure_id) REFERENCES public.structures (id) ON DELETE CASCADE;

ALTER TABLE maintenance ADD FOREIGN KEY(power_unit_id) REFERENCES public.power_units (id);

ALTER TABLE maintenance ADD FOREIGN KEY(maintenance_type_id) REFERENCES public.maintenance_types (id) ON DELETE CASCADE;

ALTER TABLE modbus_holding_registers ALTER COLUMN holding_reg_40k TYPE SMALLINT;

ALTER TABLE modbus_holding_registers DROP CONSTRAINT modbus_holding_registers_abbrev_id_fkey;

ALTER TABLE modbus_holding_registers ADD FOREIGN KEY(abbrev_id) REFERENCES public.map_abbrev_item (id);

COMMENT ON TABLE modbus_holding_registers IS NULL;

ALTER TABLE model_types DROP CONSTRAINT model_types_unit_type_id_fkey;

ALTER TABLE model_types DROP CONSTRAINT model_types_part_id_fkey;

ALTER TABLE model_types ADD FOREIGN KEY(part_id) REFERENCES public.parts (id);

ALTER TABLE model_types ADD FOREIGN KEY(unit_type_id) REFERENCES public.unit_types (id);

ALTER TABLE model_types_options DROP CONSTRAINT model_types_options_part_id_fkey;

ALTER TABLE model_types_options ADD FOREIGN KEY(part_id) REFERENCES public.parts (id);

ALTER TABLE model_types_options_rel DROP CONSTRAINT model_types_options_rel_model_id_fkey;

ALTER TABLE model_types_options_rel DROP CONSTRAINT model_types_options_rel_option_id_fkey;

ALTER TABLE model_types_options_rel ADD FOREIGN KEY(option_id) REFERENCES public.parts (id);

ALTER TABLE model_types_options_rel ADD FOREIGN KEY(model_id) REFERENCES public.parts (id);

ALTER TABLE model_types_parts_pm_seal_kits_rel DROP CONSTRAINT model_types_parts_pm_seal_kits_rel_model_type_id_fkey;

ALTER TABLE model_types_parts_pm_seal_kits_rel DROP CONSTRAINT model_types_parts_pm_seal_kits_rel_part_id_fkey;

ALTER TABLE model_types_parts_pm_seal_kits_rel ADD FOREIGN KEY(model_type_id) REFERENCES public.model_types (id);

ALTER TABLE model_types_parts_pm_seal_kits_rel ADD FOREIGN KEY(part_id) REFERENCES public.parts (id);

ALTER TABLE model_types_parts_rel DROP CONSTRAINT model_types_parts_rel_part_id_fkey;

ALTER TABLE model_types_parts_rel DROP CONSTRAINT model_types_parts_rel_model_type_id_fkey;

ALTER TABLE model_types_parts_rel ADD FOREIGN KEY(part_id) REFERENCES public.parts (id);

ALTER TABLE model_types_parts_rel ADD FOREIGN KEY(model_type_id) REFERENCES public.model_types (id);

COMMENT ON COLUMN mqtt_messages.rc IS NULL;

COMMENT ON COLUMN mqtt_messages.rc_message IS NULL;

ALTER TABLE mqtt_messages DROP CONSTRAINT mqtt_messages_gateway_id_fkey;

ALTER TABLE mqtt_messages ADD FOREIGN KEY(gateway_id) REFERENCES public.gw (id) ON DELETE CASCADE;

DROP INDEX idx_part_categories_code;

ALTER TABLE part_categories DROP CONSTRAINT part_categories_code_key;

CREATE UNIQUE INDEX ix_public_part_categories_code ON part_categories (code);

ALTER TABLE part_categories DROP CONSTRAINT part_categories_parent_category_id_fkey;

ALTER TABLE part_categories ADD FOREIGN KEY(parent_category_id) REFERENCES public.part_categories (id);

ALTER TABLE parts ALTER COLUMN no_delete DROP NOT NULL;

COMMENT ON COLUMN parts.no_delete IS NULL;

ALTER TABLE parts ALTER COLUMN is_usd SET NOT NULL;

COMMENT ON COLUMN parts.category_id IS NULL;

COMMENT ON COLUMN parts.unit_of_measure IS NULL;

COMMENT ON COLUMN parts.unit_cost IS NULL;

COMMENT ON COLUMN parts.is_serialized IS NULL;

COMMENT ON COLUMN parts.is_lot_tracked IS NULL;

COMMENT ON COLUMN parts.min_stock_level IS NULL;

COMMENT ON COLUMN parts.max_stock_level IS NULL;

COMMENT ON COLUMN parts.reorder_point IS NULL;

COMMENT ON COLUMN parts.reorder_quantity IS NULL;

COMMENT ON COLUMN parts.lead_time_days IS NULL;

COMMENT ON COLUMN parts.is_active IS NULL;

COMMENT ON COLUMN parts.is_purchasable IS NULL;

COMMENT ON COLUMN parts.is_sellable IS NULL;

COMMENT ON COLUMN parts.created_by_id IS NULL;

COMMENT ON COLUMN parts.updated_by_id IS NULL;

DROP INDEX idx_parts_category_id;

DROP INDEX idx_parts_created_by_id;

DROP INDEX idx_parts_is_active;

DROP INDEX idx_parts_is_lot_tracked;

DROP INDEX idx_parts_is_purchasable;

DROP INDEX idx_parts_is_sellable;

DROP INDEX idx_parts_is_serialized;

DROP INDEX idx_parts_unit_of_measure;

DROP INDEX idx_parts_updated_by_id;

ALTER TABLE parts DROP CONSTRAINT parts_unique_part_num;

CREATE UNIQUE INDEX ix_public_parts_part_num ON parts (part_num);

ALTER TABLE parts DROP CONSTRAINT parts_created_by_id_fkey;

ALTER TABLE parts DROP CONSTRAINT parts_updated_by_id_fkey;

ALTER TABLE parts DROP CONSTRAINT parts_category_id_fkey;

ALTER TABLE parts ADD FOREIGN KEY(updated_by_id) REFERENCES public.users (id);

ALTER TABLE parts ADD FOREIGN KEY(category_id) REFERENCES public.part_categories (id);

ALTER TABLE parts ADD FOREIGN KEY(created_by_id) REFERENCES public.users (id);

COMMENT ON TABLE parts IS 'Master table for all inventory parts and items';

ALTER TABLE power_unit_types DROP CONSTRAINT power_unit_types_part_id_fkey;

ALTER TABLE power_unit_types ADD FOREIGN KEY(part_id) REFERENCES public.parts (id);

ALTER TABLE power_unit_types_filters_rel DROP CONSTRAINT power_unit_types_filters_rel_power_unit_type_id_fkey;

ALTER TABLE power_unit_types_filters_rel DROP CONSTRAINT power_unit_types_filters_rel_part_filter_id_fkey;

ALTER TABLE power_unit_types_filters_rel ADD FOREIGN KEY(part_filter_id) REFERENCES public.part_filters (id) ON DELETE CASCADE;

ALTER TABLE power_unit_types_filters_rel ADD FOREIGN KEY(power_unit_type_id) REFERENCES public.power_unit_types (id) ON DELETE CASCADE;

ALTER TABLE power_unit_types_options DROP CONSTRAINT power_unit_types_options_part_id_fkey;

ALTER TABLE power_unit_types_options ADD FOREIGN KEY(part_id) REFERENCES public.parts (id);

ALTER TABLE power_unit_types_parts_rel DROP CONSTRAINT power_unit_types_parts_rel_power_unit_type_id_fkey;

ALTER TABLE power_unit_types_parts_rel DROP CONSTRAINT power_unit_types_parts_rel_part_id_fkey;

ALTER TABLE power_unit_types_parts_rel ADD FOREIGN KEY(part_id) REFERENCES public.parts (id);

ALTER TABLE power_unit_types_parts_rel ADD FOREIGN KEY(power_unit_type_id) REFERENCES public.power_unit_types (id);

ALTER TABLE power_unit_types_power DROP CONSTRAINT power_unit_types_power_part_id_fkey;

ALTER TABLE power_unit_types_power ADD FOREIGN KEY(part_id) REFERENCES public.parts (id);

ALTER TABLE power_unit_types_speeds DROP CONSTRAINT power_unit_types_speeds_part_id_fkey;

ALTER TABLE power_unit_types_speeds ADD FOREIGN KEY(part_id) REFERENCES public.parts (id);

ALTER TABLE power_unit_types_voltage DROP CONSTRAINT power_unit_types_voltage_part_id_fkey;

ALTER TABLE power_unit_types_voltage ADD FOREIGN KEY(part_id) REFERENCES public.parts (id);

COMMENT ON COLUMN power_units.website_card_msg IS NULL;

ALTER TABLE power_units ALTER COLUMN alerts_edge SET NOT NULL;

ALTER TABLE power_units ALTER COLUMN change_detect_sens DROP NOT NULL;

ALTER TABLE power_units ALTER COLUMN wait_time_mins_spm DROP NOT NULL;

ALTER TABLE power_units ALTER COLUMN wait_time_mins_hyd_temp TYPE INTEGER;

ALTER TABLE power_units ALTER COLUMN wait_time_mins_hyd_oil_lvl TYPE INTEGER;

ALTER TABLE power_units ALTER COLUMN wait_time_mins_hyd_filt_life TYPE INTEGER;

ALTER TABLE power_units ALTER COLUMN wait_time_mins_hyd_oil_life TYPE INTEGER;

DROP INDEX power_units_index_power_unit;

ALTER TABLE power_units DROP CONSTRAINT power_units_fk_power_unit_type_id;

ALTER TABLE power_units ADD FOREIGN KEY(power_unit_type_id) REFERENCES public.power_unit_types (id);

ALTER TABLE power_units DROP COLUMN ip_modbus;

ALTER TABLE power_units DROP COLUMN subnet_modbus;

ALTER TABLE power_units DROP COLUMN gateway_modbus;

ALTER TABLE power_units_fixed_ip_networks DROP CONSTRAINT power_units_fixed_ip_networks_power_unit_id_fkey;

ALTER TABLE power_units_fixed_ip_networks ADD FOREIGN KEY(power_unit_id) REFERENCES public.power_units (id) ON DELETE CASCADE;

ALTER TABLE power_units_modbus_networks DROP CONSTRAINT power_units_modbus_networks_power_unit_id_fkey;

ALTER TABLE power_units_modbus_networks ADD FOREIGN KEY(power_unit_id) REFERENCES public.power_units (id) ON DELETE CASCADE;

ALTER TABLE profiler_measurements DROP CONSTRAINT profiler_measurements_user_id_fkey;

ALTER TABLE profiler_measurements ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE provinces ADD FOREIGN KEY(country_id) REFERENCES public.countries (id);

ALTER TABLE release_notes DROP COLUMN checksum;

ALTER TABLE remote_control ALTER COLUMN dev_test_prd TYPE TEXT;

ALTER TABLE remote_control DROP CONSTRAINT remote_control_user_id_fkey;

ALTER TABLE remote_control DROP CONSTRAINT remote_control_power_unit_id_fkey;

ALTER TABLE remote_control ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE remote_control ADD FOREIGN KEY(power_unit_id) REFERENCES public.power_units (id);

ALTER TABLE report_email_derates ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE report_email_derates_days_of_week_rel DROP CONSTRAINT report_email_derates_days_of_week_rel_report_id_fkey;

ALTER TABLE report_email_derates_days_of_week_rel DROP CONSTRAINT report_email_derates_days_of_week_rel_day_of_week_id_fkey;

ALTER TABLE report_email_derates_days_of_week_rel ADD FOREIGN KEY(report_id) REFERENCES public.report_email_derates (id) ON DELETE CASCADE;

ALTER TABLE report_email_derates_days_of_week_rel ADD FOREIGN KEY(day_of_week_id) REFERENCES public.days_of_week (id) ON DELETE CASCADE;

ALTER TABLE report_email_derates_hours_rel DROP CONSTRAINT report_email_derates_hours_rel_hour_fkey;

ALTER TABLE report_email_derates_hours_rel DROP CONSTRAINT report_email_derates_hours_rel_report_id_fkey;

ALTER TABLE report_email_derates_hours_rel ADD FOREIGN KEY(hour) REFERENCES public.hours (hour);

ALTER TABLE report_email_derates_hours_rel ADD FOREIGN KEY(report_id) REFERENCES public.report_email_derates (id) ON DELETE CASCADE;

ALTER TABLE report_email_derates_model_types_rel DROP CONSTRAINT report_email_derates_model_types_rel_report_id_fkey;

ALTER TABLE report_email_derates_model_types_rel DROP CONSTRAINT report_email_derates_model_types_rel_model_type_id_fkey;

ALTER TABLE report_email_derates_model_types_rel ADD FOREIGN KEY(report_id) REFERENCES public.report_email_derates (id) ON DELETE CASCADE;

ALTER TABLE report_email_derates_model_types_rel ADD FOREIGN KEY(model_type_id) REFERENCES public.model_types (id) ON DELETE CASCADE;

ALTER TABLE report_email_derates_unit_types_rel DROP CONSTRAINT report_email_derates_unit_types_rel_report_id_fkey;

ALTER TABLE report_email_derates_unit_types_rel ADD FOREIGN KEY(unit_type_id) REFERENCES public.unit_types (id) ON DELETE CASCADE;

ALTER TABLE report_email_derates_unit_types_rel ADD FOREIGN KEY(report_id) REFERENCES public.report_email_derates (id) ON DELETE CASCADE;

ALTER TABLE report_email_derates_warehouses_rel DROP CONSTRAINT report_email_derates_warehouses_rel_report_id_fkey;

ALTER TABLE report_email_derates_warehouses_rel DROP CONSTRAINT report_email_derates_warehouses_rel_warehouse_id_fkey;

ALTER TABLE report_email_derates_warehouses_rel ADD FOREIGN KEY(report_id) REFERENCES public.report_email_derates (id) ON DELETE CASCADE;

ALTER TABLE report_email_derates_warehouses_rel ADD FOREIGN KEY(warehouse_id) REFERENCES public.warehouses (id) ON DELETE CASCADE;

ALTER TABLE report_email_hourly DROP CONSTRAINT report_units_down_fk_user_id;

ALTER TABLE report_email_hourly ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE report_email_hourly_days_of_week_rel DROP CONSTRAINT report_email_hourly_days_of_week_rel_report_id_fkey;

ALTER TABLE report_email_hourly_days_of_week_rel DROP CONSTRAINT report_email_hourly_days_of_week_rel_day_of_week_id_fkey;

ALTER TABLE report_email_hourly_days_of_week_rel ADD FOREIGN KEY(report_id) REFERENCES public.report_email_hourly (id) ON DELETE CASCADE;

ALTER TABLE report_email_hourly_days_of_week_rel ADD FOREIGN KEY(day_of_week_id) REFERENCES public.days_of_week (id) ON DELETE CASCADE;

ALTER TABLE report_email_hourly_hours_rel DROP CONSTRAINT report_units_down_hours_rel_fk_hours;

ALTER TABLE report_email_hourly_hours_rel DROP CONSTRAINT report_email_hourly_hours_rel_hour_fkey;

ALTER TABLE report_email_hourly_hours_rel DROP CONSTRAINT report_units_down_hours_rel_fk_report_units_down;

ALTER TABLE report_email_hourly_hours_rel DROP CONSTRAINT report_email_hourly_hours_rel_report_id_fkey;

ALTER TABLE report_email_hourly_hours_rel ADD FOREIGN KEY(report_id) REFERENCES public.report_email_hourly (id) ON DELETE CASCADE;

ALTER TABLE report_email_hourly_hours_rel ADD FOREIGN KEY(hour) REFERENCES public.hours (hour);

ALTER TABLE report_email_hourly_model_types_rel DROP CONSTRAINT report_units_down_model_types_rel_fk_model_types;

ALTER TABLE report_email_hourly_model_types_rel DROP CONSTRAINT report_units_down_model_types_rel_fk_report_units_down;

ALTER TABLE report_email_hourly_model_types_rel DROP CONSTRAINT report_email_hourly_model_types_rel_report_id_fkey;

ALTER TABLE report_email_hourly_model_types_rel ADD FOREIGN KEY(report_id) REFERENCES public.report_email_hourly (id) ON DELETE CASCADE;

ALTER TABLE report_email_hourly_model_types_rel ADD FOREIGN KEY(model_type_id) REFERENCES public.model_types (id) ON DELETE CASCADE;

ALTER TABLE report_email_hourly_unit_types_rel DROP CONSTRAINT report_units_down_unit_types_rel_fk_report_units_down;

ALTER TABLE report_email_hourly_unit_types_rel DROP CONSTRAINT report_units_down_unit_types_rel_fk_unit_types;

ALTER TABLE report_email_hourly_unit_types_rel DROP CONSTRAINT report_email_hourly_unit_types_rel_report_id_fkey;

ALTER TABLE report_email_hourly_unit_types_rel ADD FOREIGN KEY(unit_type_id) REFERENCES public.unit_types (id) ON DELETE CASCADE;

ALTER TABLE report_email_hourly_unit_types_rel ADD FOREIGN KEY(report_id) REFERENCES public.report_email_hourly (id) ON DELETE CASCADE;

ALTER TABLE report_email_hourly_warehouses_rel DROP CONSTRAINT report_email_hourly_warehouses_rel_warehouse_id_fkey;

ALTER TABLE report_email_hourly_warehouses_rel DROP CONSTRAINT report_email_hourly_warehouses_rel_report_id_fkey;

ALTER TABLE report_email_hourly_warehouses_rel ADD FOREIGN KEY(report_id) REFERENCES public.report_email_hourly (id) ON DELETE CASCADE;

ALTER TABLE report_email_hourly_warehouses_rel ADD FOREIGN KEY(warehouse_id) REFERENCES public.warehouses (id) ON DELETE CASCADE;

ALTER TABLE report_email_inventory ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE report_email_inventory_days_of_week_rel DROP CONSTRAINT report_email_inventory_days_of_week_rel_report_id_fkey;

ALTER TABLE report_email_inventory_days_of_week_rel DROP CONSTRAINT report_email_inventory_days_of_week_rel_day_of_week_id_fkey;

ALTER TABLE report_email_inventory_days_of_week_rel ADD FOREIGN KEY(day_of_week_id) REFERENCES public.days_of_week (id) ON DELETE CASCADE;

ALTER TABLE report_email_inventory_days_of_week_rel ADD FOREIGN KEY(report_id) REFERENCES public.report_email_inventory (id) ON DELETE CASCADE;

ALTER TABLE report_email_inventory_hours_rel DROP CONSTRAINT report_email_inventory_hours_rel_hour_fkey;

ALTER TABLE report_email_inventory_hours_rel DROP CONSTRAINT report_email_inventory_hours_rel_report_id_fkey;

ALTER TABLE report_email_inventory_hours_rel ADD FOREIGN KEY(hour) REFERENCES public.hours (hour);

ALTER TABLE report_email_inventory_hours_rel ADD FOREIGN KEY(report_id) REFERENCES public.report_email_inventory (id) ON DELETE CASCADE;

ALTER TABLE report_email_inventory_warehouses_rel DROP CONSTRAINT report_email_inventory_warehouses_rel_warehouse_id_fkey;

ALTER TABLE report_email_inventory_warehouses_rel DROP CONSTRAINT report_email_inventory_warehouses_rel_report_id_fkey;

ALTER TABLE report_email_inventory_warehouses_rel ADD FOREIGN KEY(report_id) REFERENCES public.report_email_inventory (id) ON DELETE CASCADE;

ALTER TABLE report_email_inventory_warehouses_rel ADD FOREIGN KEY(warehouse_id) REFERENCES public.warehouses (id) ON DELETE CASCADE;

ALTER TABLE report_email_op_hours ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE report_email_op_hours_model_types_rel DROP CONSTRAINT report_email_op_hours_model_types_rel_report_id_fkey;

ALTER TABLE report_email_op_hours_model_types_rel DROP CONSTRAINT report_email_op_hours_model_types_rel_model_type_id_fkey;

ALTER TABLE report_email_op_hours_model_types_rel DROP CONSTRAINT report_email_op_hours_model_types_report_email_op_hours_id_fkey;

ALTER TABLE report_email_op_hours_model_types_rel ADD FOREIGN KEY(model_type_id) REFERENCES public.model_types (id) ON DELETE CASCADE;

ALTER TABLE report_email_op_hours_model_types_rel ADD FOREIGN KEY(report_id) REFERENCES public.report_email_op_hours (id) ON DELETE CASCADE;

ALTER TABLE report_email_op_hours_types_rel DROP CONSTRAINT report_email_op_hours_types_rel_report_email_op_hours_id_fkey;

ALTER TABLE report_email_op_hours_types_rel DROP CONSTRAINT report_email_op_hours_types_rel_report_id_fkey;

ALTER TABLE report_email_op_hours_types_rel DROP CONSTRAINT report_email_op_hours_types_r_report_email_op_hours_type_i_fkey;

ALTER TABLE report_email_op_hours_types_rel ADD FOREIGN KEY(report_id) REFERENCES public.report_email_op_hours (id) ON DELETE CASCADE;

ALTER TABLE report_email_op_hours_types_rel ADD FOREIGN KEY(report_email_op_hours_type_id) REFERENCES public.report_email_op_hours_types (id) ON DELETE CASCADE;

ALTER TABLE report_email_op_hours_unit_types_rel DROP CONSTRAINT report_email_op_hours_unit_types_rel_report_id_fkey;

ALTER TABLE report_email_op_hours_unit_types_rel DROP CONSTRAINT report_email_op_hours_unit_types_rel_unit_type_id_fkey;

ALTER TABLE report_email_op_hours_unit_types_rel DROP CONSTRAINT report_email_op_hours_unit_types__report_email_op_hours_id_fkey;

ALTER TABLE report_email_op_hours_unit_types_rel ADD FOREIGN KEY(unit_type_id) REFERENCES public.unit_types (id) ON DELETE CASCADE;

ALTER TABLE report_email_op_hours_unit_types_rel ADD FOREIGN KEY(report_id) REFERENCES public.report_email_op_hours (id) ON DELETE CASCADE;

ALTER TABLE report_email_op_hours_warehouses_rel DROP CONSTRAINT report_email_op_hours_warehouses_rel_warehouse_id_fkey;

ALTER TABLE report_email_op_hours_warehouses_rel DROP CONSTRAINT report_email_op_hours_warehouses_rel_report_id_fkey;

ALTER TABLE report_email_op_hours_warehouses_rel ADD FOREIGN KEY(warehouse_id) REFERENCES public.warehouses (id) ON DELETE CASCADE;

ALTER TABLE report_email_op_hours_warehouses_rel ADD FOREIGN KEY(report_id) REFERENCES public.report_email_op_hours (id) ON DELETE CASCADE;

ALTER TABLE sales_taxes DROP CONSTRAINT sales_taxes_province_id_fkey;

ALTER TABLE sales_taxes ADD FOREIGN KEY(province_id) REFERENCES public.provinces (id);

ALTER TABLE service DROP CONSTRAINT service_structure_id_fkey;

ALTER TABLE service DROP CONSTRAINT service_service_type_id_fkey;

ALTER TABLE service ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE SET NULL;

ALTER TABLE service ADD FOREIGN KEY(structure_id) REFERENCES public.structures (id) ON DELETE CASCADE;

ALTER TABLE service ADD FOREIGN KEY(service_type_id) REFERENCES public.service_types (id) ON DELETE CASCADE;

ALTER TABLE service_clock ALTER COLUMN time_zone_in_id SET NOT NULL;

ALTER TABLE service_clock ALTER COLUMN time_zone_out_id SET NOT NULL;

ALTER TABLE service_clock DROP CONSTRAINT service_clock_time_zone_id_fkey;

ALTER TABLE service_clock DROP CONSTRAINT service_clock_time_zone_id_in_fkey;

ALTER TABLE service_clock DROP CONSTRAINT service_clock_warehouse_id_fkey;

ALTER TABLE service_clock DROP CONSTRAINT service_clock_structure_id_fkey;

ALTER TABLE service_clock DROP CONSTRAINT service_clock_time_zone_id_out_fkey;

ALTER TABLE service_clock ADD FOREIGN KEY(structure_id) REFERENCES public.structures (id);

ALTER TABLE service_clock ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE service_clock ADD FOREIGN KEY(time_zone_in_id) REFERENCES public.time_zones (id);

ALTER TABLE service_clock ADD FOREIGN KEY(warehouse_id) REFERENCES public.warehouses (id);

ALTER TABLE service_clock ADD FOREIGN KEY(time_zone_out_id) REFERENCES public.time_zones (id);

ALTER TABLE service_clock DROP COLUMN time_zone_id;

ALTER TABLE service_emailees ADD FOREIGN KEY(user_id) REFERENCES public.users (id);

COMMENT ON TABLE service_emailees IS NULL;

ALTER TABLE sim_cards ALTER COLUMN sim_card DROP NOT NULL;

DROP INDEX sim_cards_index;

ALTER TABLE sim_cards DROP CONSTRAINT sim_cards_fk_customer_id;

ALTER TABLE sim_cards DROP CONSTRAINT sim_cards_fk_gateway_id;

ALTER TABLE sim_cards ADD FOREIGN KEY(customer_id) REFERENCES public.customers (id);

ALTER TABLE sim_cards ADD FOREIGN KEY(gateway_id) REFERENCES public.gw (id);

ALTER TABLE structure_cust_sub_group_rel DROP CONSTRAINT structure_cust_sub_group_rel_cust_sub_group_id_fkey;

ALTER TABLE structure_cust_sub_group_rel DROP CONSTRAINT structure_cust_sub_group_rel_structure_id_fkey;

ALTER TABLE structure_cust_sub_group_rel ADD FOREIGN KEY(structure_id) REFERENCES public.structures (id) ON DELETE CASCADE;

ALTER TABLE structure_cust_sub_group_rel ADD FOREIGN KEY(cust_sub_group_id) REFERENCES public.cust_sub_groups (id) ON DELETE CASCADE;

ALTER TABLE structure_customer_rel DROP CONSTRAINT structure_customer_rel_customer_id_fkey;

ALTER TABLE structure_customer_rel DROP CONSTRAINT structure_customer_rel_structure_id_fkey;

ALTER TABLE structure_customer_rel ADD FOREIGN KEY(customer_id) REFERENCES public.customers (id) ON DELETE CASCADE;

ALTER TABLE structure_customer_rel ADD FOREIGN KEY(structure_id) REFERENCES public.structures (id) ON DELETE CASCADE;

COMMENT ON COLUMN structures.has_rcom IS NULL;

COMMENT ON COLUMN structures.warehouse_id IS NULL;

ALTER TABLE structures ADD UNIQUE (structure_slave_id);

ALTER TABLE structures DROP CONSTRAINT structures_hyd_piston_type_id_fkey;

ALTER TABLE structures DROP CONSTRAINT structures_structure_slave_id_fkey;

ALTER TABLE structures DROP CONSTRAINT structures_fk_check_valve_id;

ALTER TABLE structures DROP CONSTRAINT structures_fk_rod_id;

ALTER TABLE structures DROP CONSTRAINT structures_province_id_fkey;

ALTER TABLE structures DROP CONSTRAINT structures_fk_time_zone_id;

ALTER TABLE structures DROP CONSTRAINT structures_fk_unit_type_id;

ALTER TABLE structures DROP CONSTRAINT structures_packing_gland_id_fkey;

ALTER TABLE structures DROP CONSTRAINT fk_structures_hyd_piston_type_id;

ALTER TABLE structures DROP CONSTRAINT structures_check_valve_id_fkey;

ALTER TABLE structures DROP CONSTRAINT structures_fk_barrel_id;

ALTER TABLE structures DROP CONSTRAINT structures_fk_model_type_id;

ALTER TABLE structures DROP CONSTRAINT structures_fk_shuttle_valve_id;

ALTER TABLE structures DROP CONSTRAINT structures_warehouse_id_fkey;

ALTER TABLE structures DROP CONSTRAINT structures_fk_power_unit_id;

ALTER TABLE structures ADD FOREIGN KEY(hyd_piston_type_id) REFERENCES public.hyd_piston_types (id);

ALTER TABLE structures ADD FOREIGN KEY(province_id) REFERENCES public.provinces (id);

ALTER TABLE structures ADD FOREIGN KEY(shuttle_valve_id) REFERENCES public.shuttle_valves (id);

ALTER TABLE structures ADD FOREIGN KEY(model_type_id) REFERENCES public.model_types (id);

ALTER TABLE structures ADD FOREIGN KEY(warehouse_id) REFERENCES public.warehouses (id);

ALTER TABLE structures ADD FOREIGN KEY(model_type_id_slave) REFERENCES public.model_types (id);

ALTER TABLE structures ADD FOREIGN KEY(rod_id) REFERENCES public.rods (id);

ALTER TABLE structures ADD FOREIGN KEY(packing_gland_id) REFERENCES public.packing_glands (id);

ALTER TABLE structures ADD FOREIGN KEY(time_zone_id) REFERENCES public.time_zones (id);

ALTER TABLE structures ADD FOREIGN KEY(power_unit_id) REFERENCES public.power_units (id);

ALTER TABLE structures ADD FOREIGN KEY(unit_type_id) REFERENCES public.unit_types (id);

ALTER TABLE structures ADD FOREIGN KEY(structure_slave_id) REFERENCES public.structures (id);

ALTER TABLE structures ADD FOREIGN KEY(barrel_id) REFERENCES public.barrels (id);

ALTER TABLE structures ADD FOREIGN KEY(check_valve_id) REFERENCES public.check_valves (id);

ALTER TABLE structures DROP COLUMN max_spm;

ALTER TABLE structures DROP COLUMN unogas_egas;

ALTER TABLE structures DROP COLUMN max_suction;

ALTER TABLE structures DROP COLUMN max_delta_p;

ALTER TABLE structures DROP COLUMN max_discharge_temp;

ALTER TABLE structures DROP COLUMN prev_maint_reset_hours;

ALTER TABLE structures DROP COLUMN prev_maint_reset_date;

ALTER TABLE structures DROP COLUMN max_discharge;

DROP INDEX surface_images_index_cluster_id_ml_version;

ALTER TABLE surface_images DROP CONSTRAINT surface_images_fk_pattern_id;

ALTER TABLE surface_images ADD FOREIGN KEY(pattern_id) REFERENCES public.surface_patterns (id);

COMMENT ON TABLE surface_patterns IS NULL;

ALTER TABLE time_zones DROP CONSTRAINT time_zones_country_id_fkey;

ALTER TABLE time_zones ADD FOREIGN KEY(country_id) REFERENCES public.countries (id);

ALTER TABLE user_api_tokens DROP CONSTRAINT user_api_tokens_user_id_fkey;

ALTER TABLE user_api_tokens ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE user_authentication_challenges DROP CONSTRAINT user_authentication_challenges_user_id_fkey;

ALTER TABLE user_authentication_challenges ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE user_chart_preferences DROP CONSTRAINT user_chart_preferences_user_id_fkey;

ALTER TABLE user_chart_preferences ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE user_chart_toggles DROP CONSTRAINT user_chart_toggles_user_id_fkey;

ALTER TABLE user_chart_toggles ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE user_cust_sub_group_notify_service_requests_rel DROP CONSTRAINT user_cust_sub_group_notify_service_requests_rel_user_id_fkey;

ALTER TABLE user_cust_sub_group_notify_service_requests_rel DROP CONSTRAINT user_cust_sub_group_notify_service_reque_cust_sub_group_id_fkey;

ALTER TABLE user_cust_sub_group_notify_service_requests_rel ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE user_cust_sub_group_notify_service_requests_rel ADD FOREIGN KEY(cust_sub_group_id) REFERENCES public.cust_sub_groups (id) ON DELETE CASCADE;

ALTER TABLE user_customer_rel DROP CONSTRAINT user_customer_rel_user_id_fkey;

ALTER TABLE user_customer_rel DROP CONSTRAINT user_customer_rel_customer_id_fkey;

ALTER TABLE user_customer_rel ADD FOREIGN KEY(customer_id) REFERENCES public.customers (id) ON DELETE CASCADE;

ALTER TABLE user_customer_rel ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE user_registration_challenges DROP CONSTRAINT user_registration_challenges_user_id_fkey;

ALTER TABLE user_registration_challenges ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE user_role_rel DROP CONSTRAINT user_role_rel_user_id_fkey;

ALTER TABLE user_role_rel DROP CONSTRAINT user_role_rel_role_id_fkey;

ALTER TABLE user_role_rel ADD FOREIGN KEY(role_id) REFERENCES public.roles (id) ON DELETE CASCADE;

ALTER TABLE user_role_rel ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE user_structure_maintenance_rel DROP CONSTRAINT user_structure_maintenance_rel_structure_id_fkey;

ALTER TABLE user_structure_maintenance_rel DROP CONSTRAINT user_structure_maintenance_rel_user_id_fkey;

ALTER TABLE user_structure_maintenance_rel ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE user_structure_maintenance_rel ADD FOREIGN KEY(structure_id) REFERENCES public.structures (id) ON DELETE CASCADE;

ALTER TABLE user_structure_operators_rel DROP CONSTRAINT user_structure_operators_rel_structure_id_fkey;

ALTER TABLE user_structure_operators_rel DROP CONSTRAINT user_structure_operators_rel_user_id_fkey;

ALTER TABLE user_structure_operators_rel ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE user_structure_operators_rel ADD FOREIGN KEY(structure_id) REFERENCES public.structures (id) ON DELETE CASCADE;

ALTER TABLE user_structure_remote_control_rel DROP CONSTRAINT user_structure_remote_control_rel_user_id_fkey;

ALTER TABLE user_structure_remote_control_rel DROP CONSTRAINT user_structure_remote_control_rel_structure_id_fkey;

ALTER TABLE user_structure_remote_control_rel ADD FOREIGN KEY(structure_id) REFERENCES public.structures (id) ON DELETE CASCADE;

ALTER TABLE user_structure_remote_control_rel ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE user_structure_sales_rel DROP CONSTRAINT user_structure_sales_rel_user_id_fkey;

ALTER TABLE user_structure_sales_rel DROP CONSTRAINT user_structure_sales_rel_structure_id_fkey;

ALTER TABLE user_structure_sales_rel ADD FOREIGN KEY(structure_id) REFERENCES public.structures (id) ON DELETE CASCADE;

ALTER TABLE user_structure_sales_rel ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE user_verification_codes DROP CONSTRAINT user_verification_codes_user_id_fkey;

ALTER TABLE user_verification_codes ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE user_webauthn_credentials DROP CONSTRAINT user_webauthn_credentials_user_id_fkey;

ALTER TABLE user_webauthn_credentials ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE users DROP CONSTRAINT users_time_zone_id_fkey;

ALTER TABLE users DROP CONSTRAINT users_country_id_fkey;

ALTER TABLE users ADD FOREIGN KEY(time_zone_id) REFERENCES public.time_zones (id);

ALTER TABLE users ADD CONSTRAINT users_fk_customer_id FOREIGN KEY(customer_id) REFERENCES public.customers (id) DEFERRABLE INITIALLY DEFERRED;

ALTER TABLE users ADD FOREIGN KEY(country_id) REFERENCES public.countries (id);

DROP INDEX idx_warehouse_locations_code;

DROP INDEX idx_warehouse_locations_parent;

DROP INDEX idx_warehouse_locations_warehouse;

CREATE INDEX ix_public_warehouse_locations_location_code ON warehouse_locations (location_code);

ALTER TABLE warehouse_locations DROP CONSTRAINT warehouse_locations_parent_location_id_fkey;

ALTER TABLE warehouse_locations DROP CONSTRAINT warehouse_locations_warehouse_id_fkey;

ALTER TABLE warehouse_locations ADD FOREIGN KEY(parent_location_id) REFERENCES public.warehouse_locations (id);

ALTER TABLE warehouse_locations ADD FOREIGN KEY(warehouse_id) REFERENCES public.warehouses (id) ON DELETE RESTRICT;

COMMENT ON TABLE warehouse_locations IS NULL;

ALTER TABLE warehouses ALTER COLUMN time_zone_id DROP NOT NULL;

DROP INDEX idx_warehouses_active;

DROP INDEX idx_warehouses_country;

DROP INDEX idx_warehouses_name;

DROP INDEX idx_warehouses_province;

ALTER TABLE warehouses DROP CONSTRAINT warehouses_province_id_fkey;

ALTER TABLE warehouses ADD FOREIGN KEY(country_id) REFERENCES public.countries (id);

ALTER TABLE warehouses ADD FOREIGN KEY(time_zone_id) REFERENCES public.time_zones (id);

ALTER TABLE warehouses ADD FOREIGN KEY(province_id) REFERENCES public.provinces (id);

ALTER TABLE warehouses_parts_rel ALTER COLUMN quantity_desired SET NOT NULL;

DROP INDEX idx_warehouse_part_location;

DROP INDEX idx_warehouse_part_reorder;

ALTER TABLE warehouses_parts_rel DROP CONSTRAINT warehouses_parts_rel_warehouse_id_part_id_key;

ALTER TABLE warehouses_parts_rel ADD CONSTRAINT uq_warehouse_part UNIQUE (warehouse_id, part_id);

ALTER TABLE warehouses_parts_rel DROP CONSTRAINT warehouses_parts_rel_warehouse_id_fkey;

ALTER TABLE warehouses_parts_rel DROP CONSTRAINT warehouses_parts_rel_default_location_id_fkey;

ALTER TABLE warehouses_parts_rel DROP CONSTRAINT warehouses_parts_rel_part_id_fkey;

ALTER TABLE warehouses_parts_rel ADD FOREIGN KEY(default_location_id) REFERENCES public.warehouse_locations (id);

ALTER TABLE warehouses_parts_rel ADD FOREIGN KEY(part_id) REFERENCES public.parts (id) ON DELETE RESTRICT;

ALTER TABLE warehouses_parts_rel ADD FOREIGN KEY(warehouse_id) REFERENCES public.warehouses (id) ON DELETE RESTRICT;

ALTER TABLE website_views DROP CONSTRAINT website_views_user_id_fkey;

ALTER TABLE website_views ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE work_order_model_type_rel DROP CONSTRAINT work_order_model_type_rel_work_order_id_fkey;

ALTER TABLE work_order_model_type_rel ADD FOREIGN KEY(work_order_id) REFERENCES public.work_orders (id) ON DELETE CASCADE;

ALTER TABLE work_order_model_type_rel ADD FOREIGN KEY(model_type_id) REFERENCES public.model_types (id) ON DELETE CASCADE;

ALTER TABLE work_order_power_unit_rel DROP CONSTRAINT work_order_power_unit_rel_work_order_id_fkey;

ALTER TABLE work_order_power_unit_rel DROP CONSTRAINT work_order_power_unit_rel_power_unit_id_fkey;

ALTER TABLE work_order_power_unit_rel ADD FOREIGN KEY(power_unit_id) REFERENCES public.power_units (id) ON DELETE CASCADE;

ALTER TABLE work_order_power_unit_rel ADD FOREIGN KEY(work_order_id) REFERENCES public.work_orders (id) ON DELETE CASCADE;

ALTER TABLE work_order_service_rel DROP CONSTRAINT work_order_service_rel_work_order_id_fkey;

ALTER TABLE work_order_service_rel DROP CONSTRAINT work_order_service_rel_service_id_fkey;

ALTER TABLE work_order_service_rel ADD FOREIGN KEY(work_order_id) REFERENCES public.work_orders (id) ON DELETE CASCADE;

ALTER TABLE work_order_service_rel ADD FOREIGN KEY(service_id) REFERENCES public.service (id) ON DELETE CASCADE;

ALTER TABLE work_order_structure_rel DROP CONSTRAINT work_order_structure_rel_structure_id_fkey;

ALTER TABLE work_order_structure_rel DROP CONSTRAINT work_order_structure_rel_work_order_id_fkey;

ALTER TABLE work_order_structure_rel ADD FOREIGN KEY(structure_id) REFERENCES public.structures (id) ON DELETE CASCADE;

ALTER TABLE work_order_structure_rel ADD FOREIGN KEY(work_order_id) REFERENCES public.work_orders (id) ON DELETE CASCADE;

ALTER TABLE work_order_unit_type_rel DROP CONSTRAINT work_order_unit_type_rel_work_order_id_fkey;

ALTER TABLE work_order_unit_type_rel ADD FOREIGN KEY(unit_type_id) REFERENCES public.unit_types (id) ON DELETE CASCADE;

ALTER TABLE work_order_unit_type_rel ADD FOREIGN KEY(work_order_id) REFERENCES public.work_orders (id) ON DELETE CASCADE;

ALTER TABLE work_order_upload_files DROP CONSTRAINT work_order_upload_files_work_order_id_fkey;

ALTER TABLE work_order_upload_files ADD FOREIGN KEY(work_order_id) REFERENCES public.work_orders (id) ON DELETE CASCADE;

ALTER TABLE work_order_user_rel DROP CONSTRAINT work_order_user_rel_work_order_id_fkey;

ALTER TABLE work_order_user_rel ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE work_order_user_rel ADD FOREIGN KEY(work_order_id) REFERENCES public.work_orders (id) ON DELETE CASCADE;

ALTER TABLE work_order_user_sales_rel DROP CONSTRAINT work_order_sales_user_rel_user_id_fkey;

ALTER TABLE work_order_user_sales_rel DROP CONSTRAINT work_order_user_sales_rel_work_order_id_fkey;

ALTER TABLE work_order_user_sales_rel DROP CONSTRAINT work_order_sales_user_rel_work_order_id_fkey;

ALTER TABLE work_order_user_sales_rel ADD FOREIGN KEY(user_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE work_order_user_sales_rel ADD FOREIGN KEY(work_order_id) REFERENCES public.work_orders (id) ON DELETE CASCADE;

ALTER TABLE work_orders ALTER COLUMN id DROP DEFAULT;

ALTER TABLE work_orders ALTER COLUMN is_paid SET NOT NULL;

ALTER TABLE work_orders ALTER COLUMN customer_id SET NOT NULL;

ALTER TABLE work_orders ALTER COLUMN approved_by_id SET NOT NULL;

COMMENT ON COLUMN work_orders.date_sent_for_approval IS NULL;

COMMENT ON COLUMN work_orders.invoice_approval_req IS NULL;

COMMENT ON COLUMN work_orders.has_rcom IS NULL;

COMMENT ON COLUMN work_orders.service_resolution IS NULL;

ALTER TABLE work_orders ALTER COLUMN sales_tax_rate SET NOT NULL;

ALTER TABLE work_orders ALTER COLUMN subtotal SET NOT NULL;

ALTER TABLE work_orders ALTER COLUMN discount_pct SET NOT NULL;

ALTER TABLE work_orders ALTER COLUMN sales_tax SET NOT NULL;

ALTER TABLE work_orders ALTER COLUMN total SET NOT NULL;

ALTER TABLE work_orders DROP CONSTRAINT work_orders_currency_id_fkey;

ALTER TABLE work_orders DROP CONSTRAINT work_orders_inventory_source_id_fkey;

ALTER TABLE work_orders DROP CONSTRAINT work_orders_province_id_fkey;

ALTER TABLE work_orders DROP CONSTRAINT work_orders_zip_code_id_fkey;

ALTER TABLE work_orders DROP CONSTRAINT work_orders_city_id_fkey;

ALTER TABLE work_orders DROP CONSTRAINT work_orders_county_id_fkey;

ALTER TABLE work_orders DROP CONSTRAINT work_orders_fk_customer_id;

ALTER TABLE work_orders DROP CONSTRAINT work_orders_warehouse_id_fkey;

ALTER TABLE work_orders DROP CONSTRAINT work_orders_fk_creator_id;

ALTER TABLE work_orders DROP CONSTRAINT work_orders_status_id_fkey;

ALTER TABLE work_orders DROP CONSTRAINT work_orders_service_type_id_fkey;

ALTER TABLE work_orders ADD FOREIGN KEY(province_id) REFERENCES public.provinces (id) ON DELETE SET NULL;

ALTER TABLE work_orders ADD FOREIGN KEY(service_type_id) REFERENCES public.service_types (id);

ALTER TABLE work_orders ADD FOREIGN KEY(approved_by_id) REFERENCES public.users (id) ON DELETE CASCADE;

ALTER TABLE work_orders ADD FOREIGN KEY(country_id) REFERENCES public.countries (id) ON DELETE RESTRICT;

ALTER TABLE work_orders ADD FOREIGN KEY(warehouse_id) REFERENCES public.warehouses (id) ON DELETE SET NULL;

ALTER TABLE work_orders ADD FOREIGN KEY(customer_id) REFERENCES public.customers (id);

ALTER TABLE work_orders ADD FOREIGN KEY(city_id) REFERENCES public.cities (id);

ALTER TABLE work_orders ADD FOREIGN KEY(approval_person_id) REFERENCES public.users (id);

ALTER TABLE work_orders ADD FOREIGN KEY(status_id) REFERENCES public.work_order_status (id);

ALTER TABLE work_orders ADD FOREIGN KEY(creator_id) REFERENCES public.users (id);

ALTER TABLE work_orders ADD FOREIGN KEY(inventory_source_id) REFERENCES public.inventory_sources (id);

ALTER TABLE work_orders ADD FOREIGN KEY(county_id) REFERENCES public.counties (id);

ALTER TABLE work_orders ADD FOREIGN KEY(creator_company_id) REFERENCES public.customers (id);

ALTER TABLE work_orders ADD FOREIGN KEY(zip_code_id) REFERENCES public.zip_codes (id);

ALTER TABLE work_orders ADD FOREIGN KEY(requested_by_id) REFERENCES public.users (id) ON DELETE SET NULL;

ALTER TABLE work_orders ADD FOREIGN KEY(currency_id) REFERENCES public.currencies (id) ON DELETE CASCADE;

ALTER TABLE work_orders DROP COLUMN charged_to;

ALTER TABLE work_orders DROP COLUMN requested_by;

ALTER TABLE work_orders DROP COLUMN time_start;

ALTER TABLE work_orders DROP COLUMN company_contact;

ALTER TABLE work_orders DROP COLUMN travel_time_hours;

ALTER TABLE work_orders DROP COLUMN time_end;

ALTER TABLE work_orders DROP COLUMN company_contact_email;

ALTER TABLE work_orders DROP COLUMN company_rep;

ALTER TABLE work_orders DROP COLUMN total_hours;

ALTER TABLE work_orders DROP COLUMN service_hours;

ALTER TABLE work_orders_parts ALTER COLUMN timestamp_utc_inserted TYPE DATE;

ALTER TABLE work_orders_parts ALTER COLUMN part_id DROP NOT NULL;

ALTER TABLE work_orders_parts ALTER COLUMN description SET NOT NULL;

COMMENT ON COLUMN work_orders_parts.warehouse_id IS NULL;

ALTER TABLE work_orders_parts ALTER COLUMN price SET NOT NULL;

ALTER TABLE work_orders_parts ALTER COLUMN sales_tax_rate SET NOT NULL;

ALTER TABLE work_orders_parts DROP CONSTRAINT work_orders_parts_warehouse_to_id_fkey;

ALTER TABLE work_orders_parts DROP CONSTRAINT work_orders_parts_work_order_id_fkey;

ALTER TABLE work_orders_parts DROP CONSTRAINT work_orders_parts_part_id_fkey;

ALTER TABLE work_orders_parts DROP CONSTRAINT work_orders_parts_structure_id_fkey;

ALTER TABLE work_orders_parts DROP CONSTRAINT work_orders_parts_fk_work_order_id;

ALTER TABLE work_orders_parts DROP CONSTRAINT work_orders_parts_warehouse_id_fkey;

ALTER TABLE work_orders_parts ADD FOREIGN KEY(field_tech_id) REFERENCES public.users (id) ON DELETE SET NULL;

ALTER TABLE work_orders_parts ADD FOREIGN KEY(work_order_id) REFERENCES public.work_orders (id) ON DELETE CASCADE;

ALTER TABLE work_orders_parts ADD FOREIGN KEY(part_id) REFERENCES public.parts (id) ON DELETE SET NULL;

ALTER TABLE work_orders_parts ADD FOREIGN KEY(warehouse_to_id) REFERENCES public.warehouses (id) ON DELETE SET NULL;

ALTER TABLE work_orders_parts ADD FOREIGN KEY(structure_id) REFERENCES public.structures (id) ON DELETE SET NULL;

ALTER TABLE work_orders_parts ADD FOREIGN KEY(warehouse_id) REFERENCES public.warehouses (id) ON DELETE SET NULL;

ALTER TABLE work_orders_parts DROP COLUMN pst_part_amount;

ALTER TABLE work_orders_parts DROP COLUMN gst_part_amount;

ALTER TABLE work_orders_parts DROP COLUMN gst_rate;

ALTER TABLE work_orders_parts DROP COLUMN pst_rate;

ALTER TABLE zip_code_sales_tax DROP CONSTRAINT zip_code_sales_tax_zip_code_id_fkey;

ALTER TABLE zip_code_sales_tax DROP CONSTRAINT zip_code_sales_tax_county_id_fkey;

ALTER TABLE zip_code_sales_tax DROP CONSTRAINT zip_code_sales_tax_city_id_fkey;

ALTER TABLE zip_code_sales_tax DROP CONSTRAINT zip_code_sales_tax_state_id_fkey;

ALTER TABLE zip_code_sales_tax ADD CONSTRAINT zip_code_sales_tax_zip_code_id_fkey FOREIGN KEY(zip_code_id) REFERENCES public.zip_codes (id);

ALTER TABLE zip_code_sales_tax ADD CONSTRAINT zip_code_sales_tax_county_id_fkey FOREIGN KEY(county_id) REFERENCES public.counties (id);

ALTER TABLE zip_code_sales_tax ADD CONSTRAINT zip_code_sales_tax_state_id_fkey FOREIGN KEY(state_id) REFERENCES public.provinces (id);

ALTER TABLE zip_code_sales_tax ADD CONSTRAINT zip_code_sales_tax_city_id_fkey FOREIGN KEY(city_id) REFERENCES public.cities (id);

ALTER TABLE zip_codes DROP CONSTRAINT zip_codes_city_id_fkey;

ALTER TABLE zip_codes ADD FOREIGN KEY(city_id) REFERENCES public.cities (id);

CREATE OR REPLACE VIEW vw_work_order_parts_joined AS SELECT t1.id AS work_order_part_id, date_part('year'::text, t2.date_service) AS year, date_part('month'::text, t2.date_service) AS month, t2.date_service, t1.work_order_id, str.structure_str AS structure, str.location, str.model_type_id, mt.model, mt.unit_type_id, ut.unit_type, pu.power_unit_str AS power_unit, gw.aws_thing AS gateway, t2.timestamp_utc_inserted, t1.part_id, t3.part_num, t3.worksheet AS bom_worksheet, t3.ws_row AS worksheet_row, t3.is_usd AS is_part_usd, t3.cad_per_usd, t3.is_soft_part, t3.msrp_cad, t3.msrp_usd, t3.dealer_cost_cad, t3.dealer_cost_usd, t3.ijack_corp_cost, t3.msrp_mult_cad, t3.msrp_mult_usd, t3.transfer_mult_cad_dealer, t3.transfer_mult_usd_dealer, t3.transfer_mult_inc_to_corp, CASE WHEN t3.cost_cad IS NULL THEN t1.price::double precision / t3.msrp_mult_cad ELSE t3.cost_cad::double precision END AS cost_cad, CASE WHEN t3.cost_usd IS NULL THEN t1.price::double precision / t3.msrp_mult_usd ELSE t3.cost_usd::double precision END AS cost_usd, t1.description, t1.price, t1.quantity, t1.cost_before_tax, t1.sales_tax_rate, t1.sales_tax_part_amount AS sales_tax, t1.field_tech_id, (ft.first_name::text || ' '::text) || ft.last_name::text AS credited_to, t2.requested_by_id, t2.approval_person_id, t2.service_crew, t2.invoice_approval_req, t2.location AS wo_location, t2.has_rcom, t2.service_required, t2.service_resolution, t2.is_warranty, t2.is_warranty_reason, t2.picker_truck, t2.crew_truck, t2.man_lift, t2.trailer, t2.work_done, t2.customer_po, t2.cust_work_order, t2.afe, t2.invoice_summary, t4.customer, (t5.first_name::text || ' '::text) || t5.last_name::text AS creator, t10.name AS service_type, t11.name AS inventory_source, t2.creator_company_id, t6.customer AS creator_company, t2.country_id, countries.country_name AS country, t2.currency_id, t12.name AS currency, t13.name AS province, t16.name AS county, cities.name AS city, zip_codes.zip_code, t2.subtotal AS work_order_subtotal, t2.sales_tax AS sales_tax_total, t2.total AS work_order_total, t2.discount_pct, t2.subtotal_after_discount, t2.structure_slave, t14.name AS status, t2.is_paid, t2.notes, t2.safe_work_permit_num, t2.quickbooks_num, (t15.first_name::text || ' '::text) || t15.last_name::text AS approved_by, t2.date_due, t2.terms, t2.date_sent_for_approval, t3.part_num_group, CASE WHEN lower(str.status) ~~ '%%void%%'::text THEN true ELSE false END AS is_void, CASE WHEN t2.currency_id = 1 THEN true ELSE false END AS is_usd_work_order FROM work_orders_parts t1 LEFT JOIN structures str ON str.id = t1.structure_id LEFT JOIN public.model_types mt ON mt.id = str.model_type_id LEFT JOIN public.unit_types ut ON ut.id = mt.unit_type_id LEFT JOIN power_units pu ON pu.id = str.power_unit_id LEFT JOIN gw gw ON gw.power_unit_id = pu.id LEFT JOIN work_orders t2 ON t2.id = t1.work_order_id LEFT JOIN parts t3 ON t3.id = t1.part_id LEFT JOIN public.customers t4 ON t4.id = t2.customer_id LEFT JOIN public.customers t6 ON t6.id = t2.creator_company_id LEFT JOIN public.users t5 ON t5.id = t2.creator_id LEFT JOIN public.users ft ON ft.id = t1.field_tech_id LEFT JOIN service_types t10 ON t10.id = t2.service_type_id LEFT JOIN inventory_sources t11 ON t11.id = t2.inventory_source_id LEFT JOIN currencies t12 ON t12.id = t2.currency_id LEFT JOIN public.countries countries ON countries.id = t2.country_id LEFT JOIN provinces t13 ON t13.id = t2.province_id LEFT JOIN cities cities ON cities.id = t2.city_id LEFT JOIN zip_codes ON zip_codes.id = t2.zip_code_id LEFT JOIN work_order_status t14 ON t2.status_id = t14.id LEFT JOIN public.users t15 ON t15.id = t2.approved_by_id LEFT JOIN counties t16 ON t16.id = t2.county_id WHERE t2.is_quote = false ORDER BY t2.id DESC, t1.id;

CREATE OR REPLACE VIEW vw_work_orders_by_unit AS SELECT DISTINCT ON (wo.id, pu.id, str.id) row_number() OVER () AS id, wo.id AS work_order_id, wo.date_service, cust.customer, wo.service_required, wo.work_done, wo.is_warranty, wo.is_warranty_reason, pu.id AS power_unit_id, pu.power_unit, pu.power_unit_str, put.name AS power_unit_type, str.id AS structure_id, str.structure, str.downhole, str.surface, mt.model, ut.unit_type FROM work_orders wo LEFT JOIN work_order_power_unit_rel t2 ON t2.work_order_id = wo.id LEFT JOIN power_units pu ON pu.id = t2.power_unit_id LEFT JOIN work_order_structure_rel t4 ON t4.work_order_id = wo.id LEFT JOIN structures str ON str.id = t4.structure_id LEFT JOIN public.model_types mt ON mt.id = str.model_type_id LEFT JOIN public.unit_types ut ON ut.id = mt.unit_type_id LEFT JOIN public.power_unit_types put ON put.id = pu.power_unit_type_id LEFT JOIN work_order_user_rel wour ON wour.work_order_id = wo.id LEFT JOIN public.users users ON users.id = wour.user_id LEFT JOIN public.customers cust ON wo.customer_id = cust.id;

CREATE OR REPLACE VIEW vw_sales_by_person_year AS WITH user_count_per_work_order AS ( SELECT t.work_order_id, CASE WHEN t.user_count = 0 THEN 1::bigint ELSE t.user_count END AS user_count FROM ( SELECT t2_1.id AS work_order_id, count(t31_1.user_id) AS user_count FROM work_orders t2_1 LEFT JOIN work_order_user_sales_rel t31_1 ON t31_1.work_order_id = t2_1.id GROUP BY t2_1.id) t ), work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, sum( CASE WHEN t2_1.is_warranty IS TRUE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2_1.is_warranty IS FALSE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1_1 JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id JOIN currencies fx ON fx.id = t2_1.currency_id GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id ) SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text)) AS id, date_part('year'::text, t2.date_service) AS service_year, (t3.first_name::text || ' '::text) || t3.last_name::text AS name_, sum(t1.warranty_cad / uc.user_count::numeric) AS sales_warranty, sum(t1.cost_before_tax_cad / uc.user_count::numeric) AS sales_total, sum( CASE WHEN t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_labour, sum( CASE WHEN t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_parts, sum( CASE WHEN t5.id = 1 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_new_installs, sum( CASE WHEN t5.id = 4 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_sales, sum( CASE WHEN t5.id = 2 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_repairs, sum( CASE WHEN t5.id = 3 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_parts, sum( CASE WHEN t5.id = 5 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_prevent_maint, sum( CASE WHEN t5.id = 7 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_rentals FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id LEFT JOIN work_order_user_sales_rel t31 ON t31.work_order_id = t2.id LEFT JOIN public.users t3 ON t3.id = t31.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id JOIN user_count_per_work_order uc ON uc.work_order_id = t2.id WHERE t2.customer_id <> ALL (ARRAY[1, 3]) GROUP BY (date_part('year'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, ((t3.first_name::text || ' '::text) || t3.last_name::text);

CREATE OR REPLACE VIEW vw_sales_by_person_quarter AS WITH user_count_per_work_order AS ( SELECT t.work_order_id, CASE WHEN t.user_count = 0 THEN 1::bigint ELSE t.user_count END AS user_count FROM ( SELECT t2_1.id AS work_order_id, count(t31_1.user_id) AS user_count FROM work_orders t2_1 LEFT JOIN work_order_user_sales_rel t31_1 ON t31_1.work_order_id = t2_1.id GROUP BY t2_1.id) t ), work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, sum( CASE WHEN t2_1.is_warranty IS TRUE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2_1.is_warranty IS FALSE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1_1 JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id JOIN currencies fx ON fx.id = t2_1.currency_id GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id ) SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), (date_part('quarter'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text)) AS id, date_part('year'::text, t2.date_service) AS service_year, date_part('quarter'::text, t2.date_service) AS service_quarter, (t3.first_name::text || ' '::text) || t3.last_name::text AS name_, sum(t1.warranty_cad / uc.user_count::numeric) AS sales_warranty, sum(t1.cost_before_tax_cad / uc.user_count::numeric) AS sales_total, sum( CASE WHEN t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_labour, sum( CASE WHEN t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_parts, sum( CASE WHEN t5.id = 1 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_new_installs, sum( CASE WHEN t5.id = 4 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_sales, sum( CASE WHEN t5.id = 2 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_repairs, sum( CASE WHEN t5.id = 3 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_parts, sum( CASE WHEN t5.id = 5 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_prevent_maint, sum( CASE WHEN t5.id = 7 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_rentals FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id LEFT JOIN work_order_user_sales_rel t31 ON t31.work_order_id = t2.id LEFT JOIN public.users t3 ON t3.id = t31.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id JOIN user_count_per_work_order uc ON uc.work_order_id = t2.id WHERE t2.customer_id <> ALL (ARRAY[1, 3]) GROUP BY (date_part('year'::text, t2.date_service)), (date_part('quarter'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('quarter'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, ((t3.first_name::text || ' '::text) || t3.last_name::text);

CREATE OR REPLACE VIEW vw_sales_by_person_month AS WITH user_count_per_work_order AS ( SELECT t.work_order_id, CASE WHEN t.user_count = 0 THEN 1::bigint ELSE t.user_count END AS user_count FROM ( SELECT t2_1.id AS work_order_id, count(t31_1.user_id) AS user_count FROM work_orders t2_1 LEFT JOIN work_order_user_sales_rel t31_1 ON t31_1.work_order_id = t2_1.id GROUP BY t2_1.id) t ), work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, sum( CASE WHEN t2_1.is_warranty IS TRUE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2_1.is_warranty IS FALSE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1_1 JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id JOIN currencies fx ON fx.id = t2_1.currency_id GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id ) SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text)) AS id, date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, (t3.first_name::text || ' '::text) || t3.last_name::text AS name_, sum(t1.warranty_cad / uc.user_count::numeric) AS sales_warranty, sum(t1.cost_before_tax_cad / uc.user_count::numeric) AS sales_total, sum( CASE WHEN t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_labour, sum( CASE WHEN t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_parts, sum( CASE WHEN t5.id = 1 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_new_installs, sum( CASE WHEN t5.id = 4 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_sales, sum( CASE WHEN t5.id = 2 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_repairs, sum( CASE WHEN t5.id = 3 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_parts, sum( CASE WHEN t5.id = 5 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_prevent_maint, sum( CASE WHEN t5.id = 7 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_rentals FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id LEFT JOIN work_order_user_sales_rel t31 ON t31.work_order_id = t2.id LEFT JOIN public.users t3 ON t3.id = t31.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id JOIN user_count_per_work_order uc ON uc.work_order_id = t2.id WHERE t2.customer_id <> ALL (ARRAY[1, 3]) GROUP BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('month'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, ((t3.first_name::text || ' '::text) || t3.last_name::text);

CREATE OR REPLACE VIEW vw_gw_structure_rel AS SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures FROM gw t1 LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN structures t3 ON t3.power_unit_id = t2.id WHERE t3.id IS NOT NULL ORDER BY t1.id;

CREATE OR REPLACE VIEW vw_gw_customer_rel AS SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures, t5.id AS customers FROM gw t1 LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN structures t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t3.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id WHERE t5.id IS NOT NULL ORDER BY t1.id;

CREATE OR REPLACE VIEW vw_gw_cust_sub_group_rel AS SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures, t6.id AS cust_sub_groups FROM gw t1 LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN structures t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t3.id left join public.structure_cust_sub_group_rel csr ON csr.structure_id = t3.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = csr.cust_sub_group_id WHERE t6.id IS NOT NULL ORDER BY t1.id;

CREATE OR REPLACE VIEW vw_users_roles AS SELECT id, id AS user_id, name, email, customer, job_title, roles, string_agg(unit, ', '::text) AS units FROM ( SELECT a2.customer, a2.id, a2.name, a2.email, a2.job_title, a2.roles, CASE WHEN t7.power_unit_str IS NULL AND t6.surface IS NULL THEN ''::text ELSE concat(t7.power_unit_str, ' (', t6.surface, ')') END AS unit FROM ( SELECT a1.customer, a1.id, a1.name, a1.email, a1.job_title, string_agg(a1.role_name::text, ', '::text) AS roles FROM ( SELECT t2.id, t3.customer, concat(t2.first_name, ' ', t2.last_name) AS name, t2.email, t2.job_title, t4.name AS role_name FROM public.users t2 LEFT JOIN public.user_role_rel t1 ON t1.user_id = t2.id LEFT JOIN public.customers t3 ON t3.id = t2.customer_id LEFT JOIN public.roles t4 ON t4.id = t1.role_id ORDER BY t3.customer, t2.id, t1.role_id) a1 GROUP BY a1.customer, a1.name, a1.email, a1.job_title, a1.id ORDER BY a1.customer, a1.name) a2 LEFT JOIN public.user_structure_remote_control_rel t5 ON t5.user_id = a2.id LEFT JOIN structures t6 ON t6.id = t5.structure_id LEFT JOIN power_units t7 ON t7.id = t6.power_unit_id) a3 GROUP BY customer, id, name, email, job_title, roles ORDER BY customer, id, name, email, job_title, roles;

CREATE OR REPLACE VIEW gateways AS SELECT DISTINCT ON (gw.id, cust.customer) gw.id AS gateway_id, gw.gateway, gw.aws_thing, gw.mac, pu.id AS power_unit_id, pu.power_unit, pu.power_unit_str, str.id AS structure_id, str.structure, str.structure_slave_id, str_sl.structure AS structure_slave, gw.ready_and_working, pu.apn, pu.alerts_edge, pu.change_detect_sens, tz.id AS time_zone_id, tz.time_zone, pu.wait_time_mins, pu.wait_time_mins_ol, pu.wait_time_mins_suction, pu.wait_time_mins_discharge, pu.wait_time_mins_spm, pu.wait_time_mins_stboxf, pu.wait_time_mins_hyd_temp, pu.hyd_oil_lvl_thresh, pu.hyd_filt_life_thresh, pu.hyd_oil_life_thresh, pu.wait_time_mins_hyd_oil_lvl, pu.wait_time_mins_hyd_filt_life, pu.wait_time_mins_hyd_oil_life, pu.wait_time_mins_chk_mtr_ovld, pu.wait_time_mins_pwr_fail, pu.wait_time_mins_soft_start_err, pu.wait_time_mins_grey_wire_err, pu.wait_time_mins_ae011, pu.heartbeat_enabled, pu.online_hb_enabled, pu.suction, pu.discharge, pu.spm, pu.stboxf, pu.hyd_temp, str.gps_lat, str.gps_lon, str.downhole, str.surface, str.location, str.well_license, cust.id AS customer_id, cust.customer, cust_sub.id AS cust_sub_group_id, cust_sub.name AS cust_sub_group, cust_sub.abbrev AS cust_sub_group_abbrev, str.model_type_id, mt.model, mt.unit_type_id, ut.unit_type, mt.unit_type_id AS model_unit_type_id, ut.unit_type AS model_unit_type, str.model_type_id_slave, mt_sl.model AS model_slave, mt_sl.unit_type_id AS model_unit_type_id_slave, ut_sl.unit_type AS model_unit_type_slave, sim.sim_card, (string_to_array(cust.mqtt_topic::text, ' '::text))[1] AS mqtt_topic, str.status, CASE WHEN alerts.power_unit_id IS NULL THEN false ELSE true END AS alerts FROM gw gw LEFT JOIN power_units pu ON gw.power_unit_id = pu.id LEFT JOIN structures str ON pu.id = str.power_unit_id LEFT JOIN structures str_sl ON str.structure_slave_id = str_sl.id LEFT JOIN public.structure_customer_rel str_cust_rel ON str_cust_rel.structure_id = str.id LEFT JOIN public.customers cust ON str_cust_rel.customer_id = cust.id LEFT JOIN ( SELECT DISTINCT alerts_1.power_unit_id FROM alerts alerts_1) alerts ON pu.id = alerts.power_unit_id LEFT JOIN public.time_zones tz ON str.time_zone_id = tz.id LEFT JOIN sim_cards sim ON gw.id = sim.gateway_id LEFT JOIN public.model_types mt ON str.model_type_id = mt.id LEFT JOIN public.unit_types ut ON str.unit_type_id = ut.id LEFT JOIN public.model_types mt_sl ON str_sl.model_type_id_slave = mt_sl.id LEFT JOIN public.unit_types ut_sl ON str_sl.unit_type_id = ut_sl.id LEFT JOIN public.structure_cust_sub_group_rel csr ON csr.structure_id = str.id LEFT JOIN public.cust_sub_groups cust_sub ON cust_sub.id = csr.cust_sub_group_id ORDER BY gw.id, cust.customer;

CREATE OR REPLACE VIEW vw_structures_by_model AS SELECT row_number() OVER () AS id, t2.model, max(t1.structure_install_date) AS most_recent_install_date, sum( CASE WHEN t1.model_type_id IS NOT NULL THEN 1 ELSE 0 END) AS total_units FROM structures t1 LEFT JOIN public.model_types t2 ON t1.model_type_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id WHERE (t2.model <> ALL (ARRAY['SHOP'::text, 'TEST'::text])) AND t4.customer_id IS DISTINCT FROM 21 GROUP BY t2.model ORDER BY t2.model;

CREATE OR REPLACE VIEW vw_website_most_active_users AS SELECT row_number() OVER () AS id, t1.page, count(*) AS count_, t3.customer, t2.first_name, t2.last_name, t2.email, t2.phone, min(t1.timestamp_utc) AS earliest_date_in_sample, t2.customer_id FROM public.website_views t1 LEFT JOIN public.users t2 ON t2.id = t1.user_id LEFT JOIN public.customers t3 ON t3.id = t2.customer_id WHERE t1.user_id IS NOT NULL AND t1.page::text !~~ '%%media%%'::text AND t1.page::text !~~ '%%protected%%'::text AND (t2.customer_id <> ALL (ARRAY[1, 21])) GROUP BY t2.first_name, t2.last_name, t2.email, t2.phone, t3.customer, t2.customer_id, t1.page ORDER BY t1.page, (count(*)) DESC, t3.customer, t2.first_name, t2.last_name;

CREATE OR REPLACE VIEW vw_profiler AS SELECT t1.id, (t2.first_name::text || ' '::text) || t2.last_name::text AS full_name, t3.customer, t1.status_code, t1.timestamp_utc_started, t1.timestamp_utc_ended, t1.elapsed, t1.cpu_start, t1.cpu_end, t1.endpoint_name, t1.referrer, t1.method, t1.args, t1.kwargs, t1.query_string, t1.form, t1.ip, t1.files, t1.path, t1.request_args, t1.scheme, t1.user_agent, t1.body, t1.headers FROM public.profiler_measurements t1 LEFT JOIN public.users t2 ON t2.id = t1.user_id LEFT JOIN public.customers t3 ON t3.id = t2.customer_id WHERE t1.elapsed > 3::numeric ORDER BY t1.timestamp_utc_ended DESC;

CREATE OR REPLACE VIEW vw_gw_not_connected_dont_worry_latest AS SELECT t2.gateway_id, t2.days_since_reported, t2.timestamp_utc_last_reported, t1.timestamp_utc AS timestamp_utc_worried, t1.am_i_worried, t1.why_worried, t1.operators_contacted FROM gw_info t2 LEFT JOIN ( SELECT a1.id, a1.row_num, a1.gateway_id, a1.am_i_worried, a1.timestamp_utc, a1.why_worried, a1.operators_contacted FROM ( SELECT gw_not_connected_dont_worry.id, ( SELECT row_number() OVER (PARTITION BY gw_not_connected_dont_worry.gateway_id ORDER BY gw_not_connected_dont_worry.timestamp_utc DESC) AS row_number) AS row_num, gw_not_connected_dont_worry.gateway_id, gw_not_connected_dont_worry.am_i_worried, gw_not_connected_dont_worry.timestamp_utc, gw_not_connected_dont_worry.notes AS why_worried, gw_not_connected_dont_worry.operators_contacted FROM gw_not_connected_dont_worry) a1 WHERE a1.row_num < 2 ORDER BY a1.gateway_id) t1 ON t1.gateway_id = t2.gateway_id WHERE t2.timestamp_utc_last_reported < t1.timestamp_utc ORDER BY t2.gateway_id, t1.timestamp_utc DESC;

CREATE OR REPLACE VIEW vw_structures_joined AS SELECT DISTINCT ON (t1.structure, t4.customer_id) t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN t61.cust_sub_group_id IS NULL THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM structures t1 LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN gw t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id LEFT JOIN public.users t8 ON t8.id = t7.user_id LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id ORDER BY t1.structure;

CREATE OR REPLACE VIEW vw_structures_joined_filtered AS SELECT DISTINCT ON (t1.structure, t4.customer_id) t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN t61.cust_sub_group_id IS NULL THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM structures t1 LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN gw t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id LEFT JOIN public.users t8 ON t8.id = t7.user_id LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id WHERE t4.customer_id IS DISTINCT FROM 21 AND t1.structure_install_date IS NOT NULL AND t10.model IS NOT NULL AND (t1.model_type_id <> ALL (ARRAY[63, 41, 23, 37, 38])) AND t1.surface IS NOT NULL AND t4.customer_id IS NOT NULL AND (t4.customer_id <> ALL (ARRAY[1, 2, 3])) AND (t1.unit_type_id = 5 OR t1.unit_type_id <> 5 AND t1.time_zone_id IS NOT NULL) ORDER BY t1.structure;

CREATE OR REPLACE VIEW vw_structures_all AS SELECT t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN t61.cust_sub_group_id IS NULL THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM structures t1 LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN gw t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id LEFT JOIN public.users t8 ON t8.id = t7.user_id LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id ORDER BY t1.structure;

CREATE OR REPLACE VIEW vw_structures_all_filtered AS SELECT t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN t61.cust_sub_group_id IS NULL THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM structures t1 LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN gw t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id LEFT JOIN public.users t8 ON t8.id = t7.user_id LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id WHERE t4.customer_id IS DISTINCT FROM 21 AND t1.structure_install_date IS NOT NULL AND t10.model IS NOT NULL AND (t1.model_type_id <> ALL (ARRAY[63, 41, 23, 37, 38])) AND t1.surface IS NOT NULL AND t4.customer_id IS NOT NULL AND (t4.customer_id <> ALL (ARRAY[1, 2, 3])) AND (t1.unit_type_id = 5 OR t1.unit_type_id <> 5 AND t1.time_zone_id IS NOT NULL) ORDER BY t1.structure;

CREATE OR REPLACE VIEW vw_hours_billed_by_field_tech_by_work_order AS WITH work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, t2_1.is_warranty, t1_1.field_tech_id AS user_id, sum(t1_1.quantity) AS quantity_billed, sum( CASE WHEN t2_1.is_warranty IS TRUE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2_1.is_warranty IS FALSE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1_1 JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id JOIN currencies fx ON fx.id = t2_1.currency_id WHERE (t1_1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2_1.customer_id <> ALL (ARRAY[1, 3])) GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, t2_1.is_warranty, t1_1.field_tech_id ), service_clock_hours_by_year_month_user_id AS ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out))) AS service_year, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out))) AS service_month, t1_1.user_id, (t2_1.first_name::text || ' '::text) || t2_1.last_name::text AS name_, sum(EXTRACT(epoch FROM t1_1.total_hours_worked) / 3600::numeric) AS hours_worked_clock FROM service_clock t1_1 JOIN public.users t2_1 ON t2_1.id = t1_1.user_id WHERE t1_1.total_hours_worked IS NOT NULL GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))), t1_1.user_id, t2_1.first_name, t2_1.last_name ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))) DESC, t1_1.user_id, t2_1.first_name, t2_1.last_name ) SELECT t2.id AS work_order_id, date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, t2.is_warranty, (t3.first_name::text || ' '::text) || t3.last_name::text AS name_, t1.user_id, t2.date_service, cust.customer, avg(sc.hours_worked_clock) AS monthly_hours_worked_clock, sum(t1.quantity_billed) AS quantity_hours_billed, sum(t1.warranty_cad) AS sales_warranty, sum(t1.cost_before_tax_cad) AS sales_total, sum( CASE WHEN t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878]) THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS sales_labour, sum( CASE WHEN t1.part_id <> ALL (ARRAY[5865, 874, 881, 52644, 880, 879, 878]) THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS sales_parts, sum( CASE WHEN t5.id = 1 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_new_installs, sum( CASE WHEN t5.id = 4 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_sales, sum( CASE WHEN t5.id = 2 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_repairs, sum( CASE WHEN t5.id = 3 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_parts, sum( CASE WHEN t5.id = 5 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_prevent_maint, sum( CASE WHEN t5.id = 7 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_rentals FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN public.customers cust ON cust.id = t2.customer_id JOIN public.users t3 ON t3.id = t1.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id LEFT JOIN service_clock_hours_by_year_month_user_id sc ON sc.service_year = date_part('year'::text, t2.date_service) AND sc.service_month = date_part('month'::text, t2.date_service) AND sc.user_id = t1.user_id WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3])) GROUP BY t2.id, t2.date_service, t2.is_warranty, cust.customer, t1.user_id, ((t3.first_name::text || ' '::text) || t3.last_name::text), (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('month'::text, t2.date_service)) DESC, t2.is_warranty, ((t3.first_name::text || ' '::text) || t3.last_name::text), t2.date_service DESC;

CREATE OR REPLACE VIEW vw_hours_billed_by_field_tech_monthly_efficiency AS WITH work_orders_parts_cad AS ( SELECT t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, t1.field_tech_id AS user_id, sum(t1.quantity) AS quantity_billed, sum( CASE WHEN t2.is_warranty IS TRUE THEN 0::numeric ELSE t1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2.is_warranty IS FALSE THEN 0::numeric ELSE t1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN currencies fx ON fx.id = t2.currency_id WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3])) GROUP BY t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, t1.field_tech_id ), service_clock_hours_by_year_month_user_id AS ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_year, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_month, t1.user_id, (t2.first_name::text || ' '::text) || t2.last_name::text AS name_, sum(EXTRACT(epoch FROM t1.total_hours_worked) / 3600::numeric) AS hours_worked_clock FROM service_clock t1 JOIN public.users t2 ON t2.id = t1.user_id WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, t1.user_id, t2.first_name, t2.last_name ) SELECT row_number() OVER (ORDER BY service_year, service_month, full_name, user_id) AS id, service_year, service_month, full_name, user_id, CASE WHEN monthly_hours_worked_clock = 0::numeric THEN 0::numeric ELSE quantity_hours_billed / monthly_hours_worked_clock END AS billed_per_hour_worked, quantity_hours_billed, monthly_hours_worked_clock FROM ( SELECT date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, (t3.first_name::text || ' '::text) || t3.last_name::text AS full_name, t1.user_id, avg(sc.hours_worked_clock) AS monthly_hours_worked_clock, sum(t1.quantity_billed) AS quantity_hours_billed FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN public.customers t7 ON t7.id = t2.customer_id JOIN public.users t3 ON t3.id = t1.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id LEFT JOIN service_clock_hours_by_year_month_user_id sc ON sc.service_year = date_part('year'::text, t2.date_service) AND sc.service_month = date_part('month'::text, t2.date_service) AND sc.user_id = t1.user_id WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND t2.customer_id <> 1 GROUP BY t1.user_id, ((t3.first_name::text || ' '::text) || t3.last_name::text), (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service))) a1 ORDER BY service_year DESC, service_month DESC, full_name;

CREATE OR REPLACE VIEW vw_hours_billed_monthly_efficiency AS WITH work_orders_parts_cad AS ( SELECT t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, sum(t1.quantity) AS quantity_billed, sum( CASE WHEN t2.is_warranty IS TRUE THEN 0::numeric ELSE t1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2.is_warranty IS FALSE THEN 0::numeric ELSE t1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN currencies fx ON fx.id = t2.currency_id WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3])) GROUP BY t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id ), service_clock_hours_by_year_month_user_id AS ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_year, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_month, sum(EXTRACT(epoch FROM t1.total_hours_worked) / 3600::numeric) AS hours_worked_clock FROM service_clock t1 WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC ) SELECT row_number() OVER (ORDER BY service_year, service_month) AS id, service_year, service_month, CASE WHEN monthly_hours_worked_clock = 0::numeric THEN 0::numeric ELSE quantity_hours_billed / monthly_hours_worked_clock END AS billed_per_hour_worked, quantity_hours_billed, monthly_hours_worked_clock FROM ( SELECT date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, avg(sc.hours_worked_clock) AS monthly_hours_worked_clock, sum(t1.quantity_billed) AS quantity_hours_billed FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN public.customers t7 ON t7.id = t2.customer_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id LEFT JOIN service_clock_hours_by_year_month_user_id sc ON sc.service_year = date_part('year'::text, t2.date_service) AND sc.service_month = date_part('month'::text, t2.date_service) WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND t2.customer_id <> 1 GROUP BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service))) a1 ORDER BY service_year DESC, service_month DESC;

CREATE OR REPLACE VIEW vw_service_clock_hours_daily AS SELECT row_number() OVER (ORDER BY a1.year_, a1.month_, a1.day_, a1.name_) AS id, a1.year_, a1.month_, a1.day_, a1.user_id, a1.name_, a1.days_worked, a1.time_records, a1.hours_worked, a2.total_hours, a2.service_hours, a2.travel_hours FROM ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS month_, date_part('day'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS day_, t1.user_id, (t2.first_name::text || ' '::text) || t2.last_name::text AS name_, date_part('epoch'::text, sum(t1.total_hours_worked)) / 3600::double precision AS hours_worked, date_part('epoch'::text, sum(t1.total_hours_worked)) / 86400::double precision AS days_worked, string_agg(concat(to_char(timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_in)), 'MM-DD HH24:MI'::text), ' - ', to_char(timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)), 'MM-DD HH24:MI'::text)), ', '::text ORDER BY t1.timestamp_utc_out) AS time_records FROM service_clock t1 JOIN public.users t2 ON t2.id = t1.user_id WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('day'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1 LEFT JOIN ( SELECT work_orders.user_id, date_part('year'::text, work_orders.date_service) AS year_, date_part('month'::text, work_orders.date_service) AS month_, date_part('day'::text, work_orders.date_service) AS day_, sum(work_orders.travel_hours) AS travel_hours, sum(work_orders.service_hours) AS service_hours, sum(work_orders.total_hours) AS total_hours FROM ( SELECT t1.date_service, CASE WHEN t2.user_id IS NULL THEN t1.creator_id ELSE t2.user_id END AS user_id, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS travel_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS service_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS total_hours FROM work_orders t1 LEFT JOIN work_order_user_rel t2 ON t1.id = t2.work_order_id LEFT JOIN ( SELECT work_order_user_rel.work_order_id, count(DISTINCT work_order_user_rel.user_id) AS n_users FROM work_order_user_rel GROUP BY work_order_user_rel.work_order_id) t3 ON t1.id = t3.work_order_id) work_orders GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service)), (date_part('month'::text, work_orders.date_service)), (date_part('day'::text, work_orders.date_service)), (work_orders.date_service::timestamp without time zone)) a2 ON a1.year_ = a2.year_ AND a1.month_ = a2.month_ AND a1.day_ = a2.day_ AND a1.user_id = a2.user_id ORDER BY a1.year_, a1.month_, a1.day_, a1.user_id;

CREATE OR REPLACE VIEW vw_service_clock_hours_monthly AS SELECT row_number() OVER (ORDER BY a1.year_, a1.month_, a1.name_) AS id, a1.year_, a1.month_, a1.user_id, a1.name_, a1.days_worked, a1.hours_worked, a2.total_hours, a2.service_hours, a2.travel_hours FROM ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS month_, t1.user_id, (t2.first_name::text || ' '::text) || t2.last_name::text AS name_, date_part('epoch'::text, sum(t1.total_hours_worked)) / 3600::double precision AS hours_worked, date_part('epoch'::text, sum(t1.total_hours_worked)) / 86400::double precision AS days_worked FROM service_clock t1 JOIN public.users t2 ON t2.id = t1.user_id WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1 LEFT JOIN ( SELECT work_orders.user_id, date_part('year'::text, work_orders.date_service) AS year_, date_part('month'::text, work_orders.date_service) AS month_, sum(work_orders.travel_hours) AS travel_hours, sum(work_orders.service_hours) AS service_hours, sum(work_orders.total_hours) AS total_hours FROM ( SELECT t1.date_service, CASE WHEN t2.user_id IS NULL THEN t1.creator_id ELSE t2.user_id END AS user_id, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS travel_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS service_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS total_hours FROM work_orders t1 LEFT JOIN work_order_user_rel t2 ON t1.id = t2.work_order_id LEFT JOIN ( SELECT work_order_user_rel.work_order_id, count(DISTINCT work_order_user_rel.user_id) AS n_users FROM work_order_user_rel GROUP BY work_order_user_rel.work_order_id) t3 ON t1.id = t3.work_order_id) work_orders GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service)), (date_part('month'::text, work_orders.date_service))) a2 ON a1.year_ = a2.year_ AND a1.month_ = a2.month_ AND a1.user_id = a2.user_id ORDER BY a1.year_, a1.month_, a1.user_id;

CREATE OR REPLACE VIEW vw_service_clock_hours_yearly AS SELECT row_number() OVER (ORDER BY a1.year_, a1.name_) AS id, a1.year_, a1.user_id, a1.name_, a1.days_worked, a1.hours_worked, a2.total_hours, a2.service_hours, a2.travel_hours FROM ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_, t1.user_id, (t2.first_name::text || ' '::text) || t2.last_name::text AS name_, date_part('epoch'::text, sum(t1.total_hours_worked)) / 3600::double precision AS hours_worked, date_part('epoch'::text, sum(t1.total_hours_worked)) / 86400::double precision AS days_worked FROM service_clock t1 JOIN public.users t2 ON t2.id = t1.user_id WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1 LEFT JOIN ( SELECT work_orders.user_id, date_part('year'::text, work_orders.date_service) AS year_, sum(work_orders.travel_hours) AS travel_hours, sum(work_orders.service_hours) AS service_hours, sum(work_orders.total_hours) AS total_hours FROM ( SELECT t1.date_service, CASE WHEN t2.user_id IS NULL THEN t1.creator_id ELSE t2.user_id END AS user_id, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS travel_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS service_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS total_hours FROM work_orders t1 LEFT JOIN work_order_user_rel t2 ON t1.id = t2.work_order_id LEFT JOIN ( SELECT work_order_user_rel.work_order_id, count(DISTINCT work_order_user_rel.user_id) AS n_users FROM work_order_user_rel GROUP BY work_order_user_rel.work_order_id) t3 ON t1.id = t3.work_order_id) work_orders GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service))) a2 ON a1.year_ = a2.year_ AND a1.user_id = a2.user_id ORDER BY a1.year_, a1.user_id;

DROP VIEW vw_gw_info_plus;

DROP VIEW pg_stat_statements_info;

DROP VIEW pg_stat_statements;

DROP VIEW vw_tundra_units_w_dgas;

DROP VIEW vw_power_units_joined;

DROP VIEW vw_alerts_sent_maint_users_joined;

DROP VIEW vw_gateways_tundra;

DROP VIEW vw_work_order_most_recent;

DROP VIEW vw_joins;

INSERT INTO alembic_version (version_num) VALUES ('fa6e690d1dc3') RETURNING alembic_version.version_num;

COMMIT;

