# app/models/models.py


from flask_dance.consumer.storage.sqla import OAuthConsumerMixin
from flask_login import UserMixin, current_user
from shared.models.models import OAuth as SharedOAuth
from shared.models.models import Structure as SharedStructure
from shared.models.models import User as SharedUser
from sqlalchemy.orm import load_only
from werkzeug.security import check_password_hash, generate_password_hash

from app import db, login_manager, user_is_ijack_employee
from app.config import (
    CUSTOMER_ID_DEMO,
)
from app.utils.simple import decode_jwt_token


@login_manager.user_loader
def load_user(user_id):
    """
    Set up user_loader for Flask-Login.

    The UserMixin provides default implementations for the methods that Flask-Login
    expects user objects to have.

    "load_only" allows you to specify a subset of columns to be retrieved from the database.
    This means only the specified columns will be fetched, and other columns will be excluded from the query,
    which reduces memory and increases performance.
    """
    # return db.session.get(User, int(user_id))
    return (
        db.session.query(User)
        .options(load_only(User.id, User.is_active, User.customer_id))
        .get(int(user_id))
    )


class User(SharedUser, UserMixin):
    @property
    def password(self):
        """Prevent password from being accessed"""
        raise AttributeError("password is not a readable attribute.")

    @password.setter
    def password(self, password):
        """Set password to a hashed password"""
        self.password_hash = generate_password_hash(password)

    def verify_password(self, password):
        """Check if hashed password matches actual password"""
        return check_password_hash(self.password_hash, password)

    @staticmethod
    def get_user_from_jwt_token(token: str):
        """
        Given a jwt-encoded token, return a User record
        if its contents are a dictionary
        """
        token_decoded = decode_jwt_token(token)
        if isinstance(token_decoded, dict):
            user_id = token_decoded.get("user_id", None)
            if user_id:
                return db.session.get(User, user_id)

        return None


class OAuth(SharedOAuth, OAuthConsumerMixin):
    """
    Create an OAuth table for storing OAuth tokens
    for modern authentication and authorization.
    NOTE: The OAuthConsumerMixin from Flask-Dance will automatically
    add the necessary fields to store OAuth information.
    """

    pass


class Structure(SharedStructure):
    def __repr__(self):
        # return '<Customer: {}>'.format(self.gateway)
        # return f'{self.structure} - {self.power_unit} - {self.surface}'
        repr_str = ""
        if user_is_ijack_employee(user_id=getattr(current_user, "id", None)):
            if isinstance(self.customers_rel, list):
                num_cust: int = len(self.customers_rel)
                if num_cust == 1:
                    repr_str = f"{str(self.customers_rel[0].customer).split()[0]} "
                elif num_cust > 1:
                    for cust in self.customers_rel:
                        if cust.id != CUSTOMER_ID_DEMO:
                            repr_str += f"{str(cust.customer).split()[0]} "
                            break
        if self.power_units_rel and self.power_units_rel.power_unit_str:
            repr_str += f"{self.power_units_rel.power_unit_str} "
        if self.structure:
            struct_str = str(self.structure).replace(".0", "")
            repr_str += f"({struct_str}) " if repr_str else f"{struct_str} "
        if self.downhole:
            repr_str += f"- {self.downhole}@" if repr_str else self.downhole
        if self.surface:
            if self.downhole:
                repr_str += self.surface
            else:
                repr_str += f" - {self.surface}" if repr_str else self.surface

        return repr_str
