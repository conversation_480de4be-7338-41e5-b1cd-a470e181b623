from datetime import date
from decimal import Decimal

from sqlalchemy import (
    Column,
    Computed,
    ForeignKey,
    Table,
)
from sqlalchemy.dialects.postgresql import (
    BIGINT,
    BOOLEAN,
    BYTEA,
    CHAR,
    DATE,
    DOUBLE_PRECISION,
    INTEGER,
    NUMERIC,
    REAL,
    TEXT,
    <PERSON><PERSON>ESTAMP,
    VARC<PERSON><PERSON>,
)
from sqlalchemy.ext.associationproxy import association_proxy
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import relationship

from shared.config import (
    CURRENCY_ID_CAD,
    CUSTOMER_ID_IJACK_INC,
    INVENTORY_SOURCE_ID_CUST_PROPERTY,
    PROVINCE_ID_SK,
    SERVICE_TYPE_ID_NEW_INSTALLATION,
    USER_ID_RICHIE,
)
from shared.models.base import Base, metadata
from shared.utils.datetime_utils import utcnow_naive

# many-to-many relationship between WorkOrder and Structure
work_order_structure_rel = Table(
    "work_order_structure_rel",
    metadata,
    Column(
        "work_order_id",
        INTEGER,
        Foreign<PERSON>ey("public.work_orders.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "structure_id",
        INTEGER,
        ForeignKey("public.structures.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    schema="public",
)

# many-to-many relationship between WorkOrder and Service
work_order_service_rel = Table(
    "work_order_service_rel",
    metadata,
    Column(
        "work_order_id",
        INTEGER,
        ForeignKey("public.work_orders.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "service_id",
        INTEGER,
        ForeignKey("public.service.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    schema="public",
)

# many-to-many relationship between WorkOrder and PowerUnit
work_order_power_unit_rel = Table(
    "work_order_power_unit_rel",
    metadata,
    Column(
        "work_order_id",
        INTEGER,
        ForeignKey("public.work_orders.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "power_unit_id",
        INTEGER,
        ForeignKey("public.power_units.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    schema="public",
)

# Many-to-many relationship on IJACK field technicians
work_order_user_rel = Table(
    "work_order_user_rel",
    metadata,
    Column(
        "work_order_id",
        INTEGER,
        ForeignKey("public.work_orders.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "user_id",
        INTEGER,
        ForeignKey("public.users.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    schema="public",
)

# Many-to-many relationship on who at IJACK gets sales credit for work orders
work_order_user_sales_rel = Table(
    "work_order_user_sales_rel",
    metadata,
    Column(
        "work_order_id",
        INTEGER,
        ForeignKey("public.work_orders.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "user_id",
        INTEGER,
        ForeignKey("public.users.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    schema="public",
)

# many-to-many relationship between WorkOrder and UnitType
work_order_unit_type_rel = Table(
    "work_order_unit_type_rel",
    metadata,
    Column(
        "work_order_id",
        INTEGER,
        ForeignKey("public.work_orders.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "unit_type_id",
        INTEGER,
        ForeignKey("public.unit_types.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    schema="public",
)

# many-to-many relationship between WorkOrder and ModelType
work_order_model_type_rel = Table(
    "work_order_model_type_rel",
    metadata,
    Column(
        "work_order_id",
        INTEGER,
        ForeignKey("public.work_orders.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "model_type_id",
        INTEGER,
        ForeignKey("public.model_types.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    schema="public",
)


class WorkOrder(Base):
    """For filling out work order forms online rather than using the spreadsheet"""

    __tablename__ = "work_orders"
    __table_args__ = {
        "schema": "public",
    }

    id = Column(INTEGER, primary_key=True)
    # Invoice number in QuickBooks
    quickbooks_num = Column(VARCHAR, nullable=True)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    timestamp_utc_updated = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    date_service = Column(DATE, nullable=False, default=date.today)
    is_quote = Column(BOOLEAN, nullable=False, default=False)
    is_tax_exempt = Column(BOOLEAN, nullable=False, default=False)

    country_id = Column(
        INTEGER, ForeignKey("public.countries.id", ondelete="RESTRICT"), nullable=False
    )
    country_rel = relationship("Country", back_populates="work_orders_rel")

    date_due = Column(TIMESTAMP, Computed("date_service"))
    # @hybrid_property
    # def date_due(self):
    #     """Invoice due date for Michelle's QuickBooks import"""
    #     return self.date_service

    terms = Column(VARCHAR, default="Net 30")
    # @hybrid_property
    # def terms(self):
    #     """Payment terms for Michelle's QuickBooks import"""
    #     return "Net 30"

    # New install, service, parts, sale, etc
    service_type_id = Column(
        INTEGER,
        ForeignKey("public.service_types.id"),
        nullable=False,
        default=SERVICE_TYPE_ID_NEW_INSTALLATION,
    )
    service_type_rel = relationship("ServiceType", back_populates="work_orders_rel")

    is_paid = Column(BOOLEAN, nullable=False, default=False)
    notes = Column(TEXT, nullable=True)

    # New unit sale, customer property, or Tundra Moosomin warehouse
    inventory_source_id = Column(
        INTEGER,
        ForeignKey("public.inventory_sources.id"),
        nullable=True,
        default=INVENTORY_SOURCE_ID_CUST_PROPERTY,
    )
    inventory_source_rel = relationship(
        "InventorySource", back_populates="work_orders_rel"
    )

    customer_id = Column(INTEGER, ForeignKey("public.customers.id"), nullable=False)
    customers_rel = relationship(
        "Customer", back_populates="work_orders_rel", foreign_keys=[customer_id]
    )

    # user_id = Column(
    #     INTEGER, ForeignKey("public.users.id", ondelete="CASCADE"), nullable=True
    # )
    # # One-to-many (one user, many work orders)
    # users_rel = relationship(
    #     "User", back_populates="work_orders_user_rel", foreign_keys=[user_id]
    # )

    # Many-to-many relationship on IJACK field technicians
    users_rel = relationship(
        "User",
        secondary=work_order_user_rel,
        back_populates="work_orders_rel",
    )
    users_sales_rel = relationship(
        "User",
        secondary=work_order_user_sales_rel,
        back_populates="work_orders_sales_rel",
    )

    # Who created/filled out/submitted the work order form?
    creator_id = Column(INTEGER, ForeignKey("public.users.id"), nullable=False)
    creator_rel = relationship(
        "User", back_populates="work_orders_creator_rel", foreign_keys=[creator_id]
    )

    # Invoice creator company - IJACK Inc (Canada) or IJACK Corp (USA).
    # If IJACK Corp, then the invoice will be in USD.
    creator_company_id = Column(
        INTEGER,
        ForeignKey("public.customers.id"),
        nullable=False,
        default=CUSTOMER_ID_IJACK_INC,
    )
    creator_company_rel = relationship(
        "Customer",
        back_populates="work_orders_creator_company_rel",
        foreign_keys=[creator_company_id],
    )

    # Richie Barry needs to approve work orders before invoices can be created (by Michelle)
    approved_by_id = Column(
        INTEGER,
        ForeignKey("public.users.id", ondelete="CASCADE"),
        nullable=False,
        default=USER_ID_RICHIE,
    )
    approved_by_rel = relationship(
        "User",
        back_populates="work_orders_approved_by_rel",
        foreign_keys=[approved_by_id],
    )
    # # has the work order invoice been sent for approval?
    date_sent_for_approval = Column(DATE)
    # has work order been approved already?
    invoice_approval_req = Column(BOOLEAN, nullable=False, default=False)

    # Invoice uploaded after being approved?
    status_id = Column(
        INTEGER,
        ForeignKey("public.work_order_status.id"),
        # TODO: Add nullable=False later, once there are no nulls?
        # nullable=False,
        default=None,
    )
    status_rel = relationship("WorkOrderStatus", back_populates="work_orders_rel")

    currency_id = Column(
        INTEGER,
        ForeignKey("public.currencies.id", ondelete="CASCADE"),
        nullable=False,
        default=CURRENCY_ID_CAD,
    )
    currency_rel = relationship("Currency", back_populates="work_orders_rel")

    # Who from the company requested the work order?
    requested_by_id = Column(
        INTEGER,
        ForeignKey("public.users.id", ondelete="SET NULL"),
        nullable=True,
    )
    requested_by_rel = relationship(
        "User",
        back_populates="work_orders_requested_by_rel",
        foreign_keys=[requested_by_id],
    )

    # Who from the company will approve the work order?
    approval_person_id = Column(INTEGER, ForeignKey("public.users.id"), nullable=True)
    approval_person_rel = relationship(
        "User",
        back_populates="work_orders_approval_person_rel",
        foreign_keys=[approval_person_id],
    )

    # work_order_num = Column(INTEGER, nullable=True)
    # charged_to = Column(VARCHAR, nullable=True)
    service_crew = Column(VARCHAR, nullable=True)
    location = Column(VARCHAR, nullable=True)
    has_rcom = Column(BOOLEAN, nullable=False, default=False)
    # motor = Column(VARCHAR, nullable=True)
    service_required = Column(TEXT, nullable=True)
    service_resolution = Column(TEXT, nullable=True)
    # Description of work performed
    work_done = Column(TEXT, nullable=True)

    # Was this a transfer to another warehouse? If so, record the new warehouse ID
    # (i.e. the new warehouse to which the part was transferred). Otherwise null.
    warehouse_id = Column(
        INTEGER,
        ForeignKey("public.warehouses.id", ondelete="SET NULL"),
        nullable=True,
    )
    warehouse_rel = relationship("Warehouse", back_populates="work_orders_rel")

    # # "Equipment"
    # model_id = Column(
    #     INTEGER, ForeignKey("public.model_types.id"), nullable=True
    # )
    # model_rel = relationship("ModelType", back_populates="work_orders_rel")
    # Many-to-many relationship - "Equipment"
    model_types_rel = relationship(
        "ModelType",
        secondary=work_order_model_type_rel,
        back_populates="work_orders_rel",
    )

    # # time_start = Column(TIME, nullable=True)
    # # time_end = Column(TIME, nullable=True)
    # service_hours = Column(
    #     NUMERIC,
    #     nullable=True,
    #     default=0,
    # )
    # travel_time_hours = Column(
    #     NUMERIC,
    #     nullable=True,
    #     default=0,
    # )

    # # @hybrid_property
    # # def total_hours(self):
    # #     """This is always the sum of service_hours and travel_time_hours"""
    # #     return self.service_hours + self.travel_time_hours
    # total_hours = Column(NUMERIC, Computed("service_hours + travel_time_hours"))

    # @total_hours.setter
    # def total_hours(self, total_hours):
    #     return self.service_hours + self.travel_time_hours

    is_warranty = Column(BOOLEAN, nullable=False, default=False)
    is_warranty_reason = Column(TEXT, nullable=True)

    picker_truck = Column(BOOLEAN, nullable=False, default=False)
    crew_truck = Column(BOOLEAN, nullable=False, default=False)
    man_lift = Column(BOOLEAN, nullable=False, default=False)
    trailer = Column(BOOLEAN, nullable=False, default=False)

    customer_po = Column(VARCHAR, nullable=True)
    cust_work_order = Column(VARCHAR, nullable=True)
    # Authorization for expenditure
    afe = Column(VARCHAR, nullable=True)
    invoice_summary = Column(TEXT, nullable=True)
    safe_work_permit_num = Column(VARCHAR, nullable=True)

    # For the signature pad
    signature_svg = Column(TEXT, nullable=True)
    signature_name = Column(VARCHAR, nullable=True)
    # When the signature was captured
    signature_date = Column(DATE, nullable=True)

    # Many-to-many relationship
    structures_rel = relationship(
        "Structure",
        secondary=work_order_structure_rel,
        back_populates="work_orders_structures_rel",
    )

    structure_slave = Column(INTEGER, nullable=True)

    # Many-to-many relationship
    power_units_rel = relationship(
        "PowerUnit",
        secondary=work_order_power_unit_rel,
        back_populates="work_orders_rel",
    )

    # Many-to-many relationship
    unit_types_rel = relationship(
        "UnitType",
        secondary=work_order_unit_type_rel,
        back_populates="work_orders_rel",
    )

    province_id = Column(
        INTEGER,
        ForeignKey("public.provinces.id", ondelete="SET NULL"),
        # nullable=False,
        default=PROVINCE_ID_SK,
    )
    province_rel = relationship("Province", back_populates="work_orders_rel")
    # NOTE: This association proxy is needed when editing existing work orders
    sales_taxes_rel = association_proxy("province_rel", "sales_taxes_rel")

    county_id = Column(
        INTEGER,
        ForeignKey("public.counties.id"),
        # Canadian work orders don't have counties, just US ones
        nullable=True,
    )
    county_rel = relationship("County", back_populates="work_orders_rel")

    city_id = Column(
        INTEGER,
        ForeignKey("public.cities.id"),
        # Canadian work orders don't have cities, just US ones
        nullable=True,
    )
    city_rel = relationship("City", back_populates="work_orders_rel")

    zip_code_id = Column(INTEGER, ForeignKey("public.zip_codes.id"), nullable=True)
    zip_code_rel = relationship("ZipCode", back_populates="work_orders_rel")

    # Save the sales tax rates as values, along with the calculated sales taxes, also as values,
    # since sales tax rates change over time
    sales_tax_rate = Column(
        NUMERIC(15, 2), nullable=False
    )  # Combined rate for backward compatibility
    gst_rate = Column(NUMERIC(15, 2), nullable=True)  # Federal GST rate (Canada)
    pst_rate = Column(NUMERIC(15, 2), nullable=True)  # Provincial PST rate (Canada)

    # Calculated values
    subtotal = Column(NUMERIC(15, 2), nullable=False)
    discount_pct = Column(NUMERIC(15, 2), nullable=False, default=0)
    subtotal_after_discount = Column(NUMERIC(15, 2), nullable=True)
    sales_tax = Column(NUMERIC(15, 2), nullable=False)  # Combined tax amount
    gst_amount = Column(NUMERIC(15, 2), nullable=True)  # Federal GST amount (Canada)
    pst_amount = Column(NUMERIC(15, 2), nullable=True)  # Provincial PST amount (Canada)
    total = Column(NUMERIC(15, 2), nullable=False)

    # One work order, many parts
    # cascade="all, delete-orphan" means if this parent work order is deleted, the child "work order part" is deleted as well
    # https://docs.sqlalchemy.org/en/14/orm/tutorial.html#deleting
    # delete-orphan cascade adds behavior to the delete cascade, such that a child object will be marked for deletion when it is
    # de-associated from the parent, not just when the parent is marked for deletion. This is a common feature when dealing with a
    # related object that is "owned" by its parent, with a NOT NULL foreign key, so that removal of the item from the parent collection
    # results in its deletion.
    work_order_parts_rel = relationship(
        "WorkOrderPart",
        back_populates="work_orders_rel",
        cascade="all, delete-orphan",
    )

    # service_rel = relationship("Service", back_populates="work_orders_rel")
    # Many-to-many relationship
    services_rel = relationship(
        "Service",
        secondary=work_order_service_rel,
        back_populates="work_orders_rel",
    )

    work_order_upload_files_rel = relationship(
        "WorkOrderUploadFile",
        back_populates="work_order_rel",
        cascade="all, delete-orphan",
    )

    hours_billed_by_field_tech_rel = relationship(
        "VwHoursBilledByFieldTechByWorkOrder",
        back_populates="work_order_rel",
        # "all" means that most operations (like save-update, merge, refresh-expire, expunge) should cascade to related objects
        # "delete-orphan" means that when a child object is removed from this relationship,
        # it should be deleted from the database if it's not referenced by any other parent.
        cascade="all, delete-orphan",
    )

    def update_subtotal(self):
        """Update the subtotal, sales tax, and total for this work order"""

        self.subtotal = Decimal("0")
        for part in self.work_order_parts_rel:
            cost_before_tax_calc = part.price * part.quantity
            self.subtotal += cost_before_tax_calc

        # Calculate the subtotal after discount
        if self.discount_pct in (None, 0, Decimal("0")):
            self.subtotal_after_discount = self.subtotal
        else:
            self.subtotal_after_discount = self.subtotal * (1 - self.discount_pct / 100)

        # Calculate taxes based on if we have split GST/PST or combined rate
        if self.gst_rate is not None and self.pst_rate is not None:
            # Canadian taxes - GST and PST separately
            self.gst_amount = (
                self.gst_rate / Decimal("100")
            ) * self.subtotal_after_discount
            self.pst_amount = (
                self.pst_rate / Decimal("100")
            ) * self.subtotal_after_discount
            self.sales_tax = self.gst_amount + self.pst_amount
            # Ensure sales_tax_rate is kept in sync for backward compatibility
            self.sales_tax_rate = self.gst_rate + self.pst_rate
        else:
            # Legacy or US taxes - single combined rate
            # Sales tax rate is stored as a number between 0 and 100, not between 0 and 1
            self.sales_tax = (
                self.sales_tax_rate / Decimal("100")
            ) * self.subtotal_after_discount
            # Set individual tax amounts to None when using combined rate
            self.gst_amount = None
            self.pst_amount = None

        self.total = self.subtotal_after_discount + self.sales_tax

    def __repr__(self):
        """How to represent this work order as a string"""
        string = f"{self.id}: {self.service_type_rel}"
        if self.customers_rel:
            string = f"{string} - {self.customers_rel}"
        # if hasattr(self, "structure") and self.structure:
        #     string = f"{string} - {self.structure}"
        # if hasattr(self, "power_unit") and self.power_unit:
        #     string = f"{string} - {self.power_unit}"
        return string


class WorkOrderPart(Base):
    """For adding any number of parts to a work order"""

    # __abstract__ = True
    __tablename__ = "work_orders_parts"
    __table_args__ = {
        "schema": "public",
    }

    id = Column(INTEGER, primary_key=True)
    timestamp_utc_inserted = Column(DATE, nullable=False, default=utcnow_naive)

    # Relationship
    work_order_id = Column(
        INTEGER,
        ForeignKey("public.work_orders.id", ondelete="CASCADE"),
        # If the parent work order is deleted, this work order part will be deleted too
        nullable=False,
    )
    part_id = Column(
        INTEGER, ForeignKey("public.parts.id", ondelete="SET NULL"), nullable=True
    )
    # Need this "part_num" association proxy for the form to populate correctly

    description = Column(TEXT, nullable=False)

    structure_id = Column(
        INTEGER,
        ForeignKey("public.structures.id", ondelete="SET NULL"),
        # Soon this will be false, since we must assign a structure for analysis purposes
        nullable=True,
    )

    warehouse_id = Column(
        INTEGER,
        ForeignKey("public.warehouses.id", ondelete="SET NULL"),
        # Lots of historical work orders don't have a warehouse, so this is nullable
        nullable=True,
    )
    warehouse_to_id = Column(
        INTEGER,
        ForeignKey("public.warehouses.id", ondelete="SET NULL"),
        # For transfers, this is the warehouse to which the part was transferred
        nullable=True,
    )

    field_tech_id = Column(
        INTEGER,
        ForeignKey("public.users.id", ondelete="SET NULL"),
        nullable=True,
    )

    # Quantity needed for this part in the work order
    quantity = Column(NUMERIC(15, 2), nullable=False, default=1)
    # Tracks actual reservations
    # I'm separating quantity_reserved from quantity_allocated because reservations are temporary (can be cancelled)
    # while allocations are permanent consumption. This follows the best practice of establishing standardized workflows
    # for parts issuance and work order handling to ensure inventory movements are consistently recorded
    quantity_reserved = Column(NUMERIC(15, 2), nullable=False, default=0)
    # Tracks final allocation of parts to the work order
    quantity_allocated = Column(NUMERIC(15, 2), nullable=False, default=0)

    # Price of the part in the work order
    price = Column(NUMERIC(15, 2), nullable=False)
    cost_before_tax = Column(NUMERIC(15, 2), Computed("quantity * price"))

    # Tax rates
    sales_tax_rate = Column(NUMERIC(15, 2), nullable=False)  # Combined rate
    # gst_rate = Column(NUMERIC, nullable=True)  # Federal GST rate (Canada)
    # pst_rate = Column(NUMERIC, nullable=True)  # Provincial PST rate (Canada)

    # Tax amounts
    sales_tax_part_amount = Column(
        NUMERIC(15, 2),
        Computed("sales_tax_rate/100 * quantity * price"),
    )
    # gst_part_amount = Column(
    #     NUMERIC,
    #     Computed(
    #         "CASE WHEN gst_rate IS NULL THEN NULL ELSE gst_rate/100 * quantity * price END"
    #     ),
    # )
    # pst_part_amount = Column(
    #     NUMERIC,
    #     Computed(
    #         "CASE WHEN pst_rate IS NULL THEN NULL ELSE pst_rate/100 * quantity * price END"
    #     ),
    # )

    # If the parent work order is deleted, this work order part will be deleted too
    # (see "work_order_part" relationship in work order table)
    @declared_attr
    def work_orders_rel(cls):
        return relationship("WorkOrder", back_populates="work_order_parts_rel")

    @declared_attr
    def quickbooks_num(cls):
        return association_proxy("work_orders_rel", "quickbooks_num")

    @declared_attr
    def status(cls):
        return association_proxy("work_orders_rel", "status")

    @declared_attr
    def date_service(cls):
        return association_proxy("work_orders_rel", "date_service")

    @declared_attr
    def location(cls):
        return association_proxy("work_orders_rel", "location")

    @declared_attr
    def province_rel(cls):
        return association_proxy("work_orders_rel", "province_rel")

    @declared_attr
    def sales_tax(cls):
        return association_proxy("work_orders_rel", "sales_tax")

    @declared_attr
    def invoice_summary(cls):
        return association_proxy("work_orders_rel", "invoice_summary")

    @declared_attr
    def approved_by_rel(cls):
        return association_proxy("work_orders_rel", "approved_by_rel")

    @declared_attr
    def creator_rel(cls):
        return association_proxy("work_orders_rel", "creator_rel")

    @declared_attr
    def invoice_approval_req(cls):
        return association_proxy("work_orders_rel", "invoice_approval_req")

    @declared_attr
    def date_due(cls):
        return association_proxy("work_orders_rel", "date_due")

    @declared_attr
    def terms(cls):
        return association_proxy("work_orders_rel", "terms")

    @declared_attr
    def county_rel(cls):
        return association_proxy("work_orders_rel", "county_rel")

    @declared_attr
    def parts_rel(cls):
        return relationship(
            "Part",
            back_populates="work_order_parts_rel",
            foreign_keys="WorkOrderPart.part_id",
        )

    @declared_attr
    def part_num(cls):
        return association_proxy("parts_rel", "part_num")

    @declared_attr
    def structures_rel(cls):
        return relationship(
            "Structure",
            back_populates="work_order_parts_rel",
            foreign_keys="WorkOrderPart.structure_id",
        )

    @declared_attr
    def field_tech_rel(cls):
        return relationship(
            "User",
            back_populates="work_order_parts_field_tech_rel",
            foreign_keys="WorkOrderPart.field_tech_id",
        )

    @declared_attr
    def warehouses_rel(cls):
        return relationship(
            "Warehouse",
            back_populates="work_order_parts_rel",
            foreign_keys="WorkOrderPart.warehouse_id",
        )

    def __repr__(self):
        try:
            return f"{self.part_num} ({self.work_orders_rel})"
        except Exception:
            return str(self.description)


class WorkOrderUploadFile(Base):
    """For files uploaded from work orders submitted"""

    __tablename__ = "work_order_upload_files"
    __table_args__ = {
        "schema": "public",
    }

    id = Column(INTEGER, primary_key=True)
    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive)

    work_order_id = Column(
        INTEGER,
        ForeignKey("public.work_orders.id", ondelete="CASCADE"),
        nullable=False,
    )
    work_order_rel = relationship(
        "WorkOrder", back_populates="work_order_upload_files_rel"
    )

    file_name = Column(VARCHAR, nullable=False)
    file_type = Column(VARCHAR, nullable=False)
    file_bytes = Column(BYTEA, nullable=False)

    def __repr__(self):
        return str(self.file_name)


class WorkOrderPartsJoined(Base):
    """View for joining work orders with parts"""

    __tablename__ = "vw_work_order_parts_joined"
    __table_args__ = {
        "schema": "public",
        "info": {
            "view": True,
            "script": r"""
SELECT t1.id AS work_order_part_id,
    date_part('year'::text, t2.date_service) AS year,
    date_part('month'::text, t2.date_service) AS month,
    t2.date_service,
    t1.work_order_id,
    str.structure_str AS structure,
    str.location,
    str.model_type_id,
    mt.model,
    mt.unit_type_id,
    ut.unit_type,
    pu.power_unit_str AS power_unit,
    gw.aws_thing AS gateway,
    t2.timestamp_utc_inserted,
    t1.part_id,
    t3.part_num,
    t3.worksheet AS bom_worksheet,
    t3.ws_row AS worksheet_row,
    t3.is_usd AS is_part_usd,
    t3.cad_per_usd,
    t3.is_soft_part,
    t3.msrp_cad,
    t3.msrp_usd,
    t3.dealer_cost_cad,
    t3.dealer_cost_usd,
    t3.ijack_corp_cost,
    t3.msrp_mult_cad,
    t3.msrp_mult_usd,
    t3.transfer_mult_cad_dealer,
    t3.transfer_mult_usd_dealer,
    t3.transfer_mult_inc_to_corp,
        CASE
            WHEN t3.cost_cad IS NULL THEN t1.price::double precision / t3.msrp_mult_cad
            ELSE t3.cost_cad::double precision
        END AS cost_cad,
        CASE
            WHEN t3.cost_usd IS NULL THEN t1.price::double precision / t3.msrp_mult_usd
            ELSE t3.cost_usd::double precision
        END AS cost_usd,
    t1.description,
    t1.price,
    t1.quantity,
    t1.cost_before_tax,
    t1.sales_tax_rate,
    t1.sales_tax_part_amount AS sales_tax,
    t1.field_tech_id,
    (ft.first_name::text || ' '::text) || ft.last_name::text AS credited_to,
    t2.requested_by_id,
    t2.approval_person_id,
    t2.service_crew,
    t2.invoice_approval_req,
    t2.location AS wo_location,
    t2.has_rcom,
    t2.service_required,
    t2.service_resolution,
    t2.is_warranty,
    t2.is_warranty_reason,
    t2.picker_truck,
    t2.crew_truck,
    t2.man_lift,
    t2.trailer,
    t2.work_done,
    t2.customer_po,
    t2.cust_work_order,
    t2.afe,
    t2.invoice_summary,
    t4.customer,
    (t5.first_name::text || ' '::text) || t5.last_name::text AS creator,
    t10.name AS service_type,
    t11.name AS inventory_source,
    t2.creator_company_id,
    t6.customer AS creator_company,
    t2.country_id,
    countries.country_name AS country,
    t2.currency_id,
    t12.name AS currency,
    t13.name AS province,
    t16.name AS county,
    cities.name AS city,
    zip_codes.zip_code,
    t2.subtotal AS work_order_subtotal,
    t2.sales_tax AS sales_tax_total,
    t2.total AS work_order_total,
    t2.discount_pct,
    t2.subtotal_after_discount,
    t2.structure_slave,
    t14.name AS status,
    t2.is_paid,
    t2.notes,
    t2.safe_work_permit_num,
    t2.quickbooks_num,
    (t15.first_name::text || ' '::text) || t15.last_name::text AS approved_by,
    t2.date_due,
    t2.terms,
    t2.date_sent_for_approval,
    t3.part_num_group,
        CASE
            WHEN lower(str.status) ~~ '%void%'::text THEN true
            ELSE false
        END AS is_void,
        CASE
            WHEN t2.currency_id = 1 THEN true
            ELSE false
        END AS is_usd_work_order
   FROM work_orders_parts t1
     LEFT JOIN structures str ON str.id = t1.structure_id
     LEFT JOIN public.model_types mt ON mt.id = str.model_type_id
     LEFT JOIN public.unit_types ut ON ut.id = mt.unit_type_id
     LEFT JOIN power_units pu ON pu.id = str.power_unit_id
     LEFT JOIN gw gw ON gw.power_unit_id = pu.id
     LEFT JOIN work_orders t2 ON t2.id = t1.work_order_id
     LEFT JOIN parts t3 ON t3.id = t1.part_id
     LEFT JOIN public.customers t4 ON t4.id = t2.customer_id
     LEFT JOIN public.customers t6 ON t6.id = t2.creator_company_id
     LEFT JOIN public.users t5 ON t5.id = t2.creator_id
     LEFT JOIN public.users ft ON ft.id = t1.field_tech_id
     LEFT JOIN service_types t10 ON t10.id = t2.service_type_id
     LEFT JOIN inventory_sources t11 ON t11.id = t2.inventory_source_id
     LEFT JOIN currencies t12 ON t12.id = t2.currency_id
     LEFT JOIN public.countries countries ON countries.id = t2.country_id
     LEFT JOIN provinces t13 ON t13.id = t2.province_id
     LEFT JOIN cities cities ON cities.id = t2.city_id
     LEFT JOIN zip_codes ON zip_codes.id = t2.zip_code_id
     LEFT JOIN work_order_status t14 ON t2.status_id = t14.id
     LEFT JOIN public.users t15 ON t15.id = t2.approved_by_id
     LEFT JOIN counties t16 ON t16.id = t2.county_id
  WHERE t2.is_quote = false
  ORDER BY t2.id DESC, t1.id
""",
        },
    }

    work_order_part_id = Column(INTEGER, primary_key=True)
    year = Column(INTEGER)
    month = Column(INTEGER)
    date_service = Column(DATE)
    work_order_id = Column(INTEGER)
    structure = Column(VARCHAR)
    location = Column(VARCHAR)
    model_type_id = Column(INTEGER)
    model = Column(VARCHAR)
    unit_type_id = Column(INTEGER)
    unit_type = Column(VARCHAR)
    power_unit = Column(VARCHAR)
    gateway = Column(VARCHAR)
    timestamp_utc_inserted = Column(TIMESTAMP)
    part_id = Column(INTEGER)
    part_num = Column(VARCHAR)
    bom_worksheet = Column(VARCHAR)
    worksheet_row = Column(VARCHAR)
    is_part_usd = Column(BOOLEAN)
    cad_per_usd = Column(NUMERIC(15, 2))
    is_soft_part = Column(BOOLEAN)
    msrp_cad = Column(NUMERIC(15, 2))
    msrp_usd = Column(NUMERIC(15, 2))
    dealer_cost_cad = Column(NUMERIC(15, 2))
    dealer_cost_usd = Column(NUMERIC(15, 2))
    ijack_corp_cost = Column(NUMERIC(15, 2))
    msrp_mult_cad = Column(NUMERIC(15, 2))
    msrp_mult_usd = Column(NUMERIC(15, 2))
    transfer_mult_cad_dealer = Column(NUMERIC(15, 2))
    transfer_mult_usd_dealer = Column(NUMERIC(15, 2))
    transfer_mult_inc_to_corp = Column(NUMERIC(15, 2))
    cost_cad = Column(NUMERIC(15, 2))
    cost_usd = Column(NUMERIC(15, 2))
    description = Column(TEXT)
    price = Column(NUMERIC(15, 2))
    quantity = Column(NUMERIC(15, 2))
    cost_before_tax = Column(NUMERIC(15, 2))
    sales_tax_rate = Column(NUMERIC(15, 2))
    sales_tax = Column(NUMERIC(15, 2))
    field_tech_id = Column(INTEGER)
    credited_to = Column(VARCHAR)
    requested_by_id = Column(INTEGER)
    approval_person_id = Column(INTEGER)
    service_crew = Column(VARCHAR)
    invoice_approval_req = Column(BOOLEAN)
    wo_location = Column(VARCHAR)
    has_rcom = Column(BOOLEAN)
    service_required = Column(TEXT)
    service_resolution = Column(TEXT)
    is_warranty = Column(BOOLEAN)
    is_warranty_reason = Column(TEXT)
    picker_truck = Column(BOOLEAN)
    crew_truck = Column(BOOLEAN)
    man_lift = Column(BOOLEAN)
    trailer = Column(BOOLEAN)
    work_done = Column(TEXT)
    customer_po = Column(VARCHAR)
    cust_work_order = Column(VARCHAR)
    afe = Column(VARCHAR)
    invoice_summary = Column(TEXT)
    customer = Column(VARCHAR)
    creator = Column(VARCHAR)
    service_type = Column(VARCHAR)
    inventory_source = Column(VARCHAR)
    creator_company_id = Column(INTEGER)
    creator_company = Column(VARCHAR)
    country_id = Column(INTEGER)
    country = Column(VARCHAR)
    currency_id = Column(INTEGER)
    currency = Column(VARCHAR)
    is_usd_work_order = Column(BOOLEAN)
    province = Column(VARCHAR)
    county = Column(VARCHAR)
    city = Column(VARCHAR)
    zip_code = Column(VARCHAR)
    work_order_subtotal = Column(NUMERIC(15, 2))
    sales_tax_total = Column(NUMERIC(15, 2))
    work_order_total = Column(NUMERIC(15, 2))
    discount_pct = Column(NUMERIC(15, 2))
    subtotal_after_discount = Column(NUMERIC(15, 2))
    structure_slave = Column(INTEGER)
    status = Column(VARCHAR)
    is_paid = Column(BOOLEAN)
    notes = Column(TEXT)
    safe_work_permit_num = Column(VARCHAR)
    quickbooks_num = Column(VARCHAR)
    approved_by = Column(VARCHAR)
    date_due = Column(DATE)
    terms = Column(VARCHAR)
    date_sent_for_approval = Column(DATE)
    part_num_group = Column(VARCHAR)
    is_void = Column(BOOLEAN)
    is_usd_work_order = Column(BOOLEAN)


class WorkOrdersByUnit(Base):
    """View for checking work orders by unit"""

    __tablename__ = "vw_work_orders_by_unit"
    __table_args__ = {
        "schema": "public",
        "info": {
            "view": True,
            "script": r"""
SELECT DISTINCT ON (wo.id, pu.id, str.id) row_number() OVER () AS id,
    wo.id AS work_order_id,
    wo.date_service,
    cust.customer,
    wo.service_required,
    wo.work_done,
    wo.is_warranty,
    wo.is_warranty_reason,
    pu.id AS power_unit_id,
    pu.power_unit,
    pu.power_unit_str,
    put.name AS power_unit_type,
    str.id AS structure_id,
    str.structure,
    str.downhole,
    str.surface,
    mt.model,
    ut.unit_type
   FROM work_orders wo
     LEFT JOIN work_order_power_unit_rel t2 ON t2.work_order_id = wo.id
     LEFT JOIN power_units pu ON pu.id = t2.power_unit_id
     LEFT JOIN work_order_structure_rel t4 ON t4.work_order_id = wo.id
     LEFT JOIN structures str ON str.id = t4.structure_id
     LEFT JOIN public.model_types mt ON mt.id = str.model_type_id
     LEFT JOIN public.unit_types ut ON ut.id = mt.unit_type_id
     LEFT JOIN public.power_unit_types put ON put.id = pu.power_unit_type_id
     LEFT JOIN work_order_user_rel wour ON wour.work_order_id = wo.id
     LEFT JOIN public.users users ON users.id = wour.user_id
     LEFT JOIN public.customers cust ON wo.customer_id = cust.id
""",
        },
    }

    id = Column(INTEGER, primary_key=True)
    work_order_id = Column(INTEGER)
    date_service = Column(DATE)
    customer = Column(VARCHAR)
    service_required = Column(VARCHAR)
    work_done = Column(VARCHAR)
    is_warranty = Column(BOOLEAN)
    is_warranty_reason = Column(VARCHAR)
    power_unit_id = Column(INTEGER)
    power_unit = Column(NUMERIC)
    power_unit_str = Column(VARCHAR)
    power_unit_type = Column(VARCHAR)
    structure_id = Column(INTEGER)
    structure = Column(NUMERIC)
    downhole = Column(VARCHAR)
    surface = Column(VARCHAR)
    model = Column(VARCHAR)
    unit_type = Column(VARCHAR)

    def __repr__(self):
        return f"work_order_id '{self.work_order_id}'. power_unit '{self.power_unit_str}'. structure '{self.structure}'."


class WorkOrderStatus(Base):
    """Status of work order"""

    __tablename__ = "work_order_status"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    name = Column(VARCHAR, nullable=False, unique=True)
    description = Column(TEXT, nullable=True)

    work_orders_rel = relationship("WorkOrder", back_populates="status_rel")

    def __repr__(self):
        return str(self.name)


class Service(Base):
    """For service requests/orders from IJACK people or customers"""

    __tablename__ = "service"
    __table_args__ = {
        "schema": "public",
    }

    id = Column(INTEGER, primary_key=True)
    timestamp_utc = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    priority = Column(INTEGER)
    description = Column(VARCHAR, nullable=False)
    is_resolved = Column(BOOLEAN, nullable=False, default=False)
    resolution = Column(TEXT, nullable=True)

    # If nullable = False, then we need to cascade delete
    # e.g. ForeignKey("public.users.id", ondelete="CASCADE"), nullable=False
    user_id = Column(
        INTEGER, ForeignKey("public.users.id", ondelete="SET NULL"), nullable=True
    )
    users_rel = relationship("User", back_populates="service_rel")

    operator_name = Column(VARCHAR)
    operator_phone = Column(CHAR(15))
    operator_email = Column(VARCHAR)

    service_type_id = Column(
        INTEGER,
        ForeignKey("public.service_types.id", ondelete="CASCADE"),
        nullable=False,
    )
    service_type_rel = relationship("ServiceType", back_populates="service_rel")

    structure_id = Column(
        INTEGER,
        ForeignKey("public.structures.id", ondelete="CASCADE"),
    )
    structures_rel = relationship("Structure", back_populates="service_rel")

    # work_orders_rel = relationship("WorkOrder", back_populates="service_rel")
    # Many-to-many relationship
    work_orders_rel = relationship(
        "WorkOrder",
        secondary=work_order_service_rel,
        back_populates="services_rel",
    )

    def __repr__(self):
        return str(self.description)


class ServiceEmailee(Base):
    """For sending emails to people who need to know about a service request"""

    __tablename__ = "service_emailees"
    __table_args__ = {
        "schema": "public",
    }

    id = Column(INTEGER, primary_key=True)
    user_id = Column(
        INTEGER, ForeignKey("public.users.id"), nullable=False, unique=True
    )
    user_rel = relationship("User", back_populates="service_emailees_rel")

    def __repr__(self):
        return str(self.user_rel)


class ServiceType(Base):
    """For filling out work order forms"""

    __tablename__ = "service_types"
    __table_args__ = {
        "schema": "public",
    }

    id = Column(INTEGER, primary_key=True)
    name = Column(VARCHAR, nullable=False, unique=True)
    description = Column(VARCHAR, nullable=True)

    work_orders_rel = relationship("WorkOrder", back_populates="service_type_rel")
    service_rel = relationship("Service", back_populates="service_type_rel")

    def __repr__(self):
        return str(self.name)


class InventorySource(Base):
    """For filling out work order forms"""

    __tablename__ = "inventory_sources"
    __table_args__ = {
        "schema": "public",
    }

    id = Column(INTEGER, primary_key=True)
    name = Column(VARCHAR, nullable=False)
    description = Column(VARCHAR, nullable=True)

    work_orders_rel = relationship("WorkOrder", back_populates="inventory_source_rel")

    def __repr__(self):
        return self.name


class VwSalesByPersonYear(Base):
    """Sales by person and year (view, not table)"""

    __tablename__ = "vw_sales_by_person_year"
    __table_args__ = {
        "schema": "public",
        "info": {
            "view": True,
            "script": r"""
WITH user_count_per_work_order AS (
         SELECT t.work_order_id,
                CASE
                    WHEN t.user_count = 0 THEN 1::bigint
                    ELSE t.user_count
                END AS user_count
           FROM ( SELECT t2_1.id AS work_order_id,
                    count(t31_1.user_id) AS user_count
                   FROM work_orders t2_1
                     LEFT JOIN work_order_user_sales_rel t31_1 ON t31_1.work_order_id = t2_1.id
                  GROUP BY t2_1.id) t
        ), work_orders_parts_cad AS (
         SELECT t1_1.work_order_id,
            t1_1.part_id,
            t2_1.date_service,
            t2_1.service_type_id,
            sum(
                CASE
                    WHEN t2_1.is_warranty IS TRUE THEN 0::numeric
                    ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per
                END) AS cost_before_tax_cad,
            sum(
                CASE
                    WHEN t2_1.is_warranty IS FALSE THEN 0::numeric
                    ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per
                END) AS warranty_cad
           FROM work_orders_parts t1_1
             JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id
             JOIN currencies fx ON fx.id = t2_1.currency_id
          GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id
        )
 SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text)) AS id,
    date_part('year'::text, t2.date_service) AS service_year,
    (t3.first_name::text || ' '::text) || t3.last_name::text AS name_,
    sum(t1.warranty_cad / uc.user_count::numeric) AS sales_warranty,
    sum(t1.cost_before_tax_cad / uc.user_count::numeric) AS sales_total,
    sum(
        CASE
            WHEN t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS sales_labour,
    sum(
        CASE
            WHEN t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS sales_parts,
    sum(
        CASE
            WHEN t5.id = 1 THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS type_new_installs,
    sum(
        CASE
            WHEN t5.id = 4 THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS type_sales,
    sum(
        CASE
            WHEN t5.id = 2 THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS type_repairs,
    sum(
        CASE
            WHEN t5.id = 3 THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS type_parts,
    sum(
        CASE
            WHEN t5.id = 5 THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS type_prevent_maint,
    sum(
        CASE
            WHEN t5.id = 7 THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS type_rentals
   FROM work_orders_parts_cad t1
     JOIN work_orders t2 ON t2.id = t1.work_order_id
     LEFT JOIN work_order_user_sales_rel t31 ON t31.work_order_id = t2.id
     LEFT JOIN public.users t3 ON t3.id = t31.user_id
     JOIN parts t4 ON t4.id = t1.part_id
     JOIN service_types t5 ON t5.id = t2.service_type_id
     JOIN user_count_per_work_order uc ON uc.work_order_id = t2.id
  WHERE t2.customer_id <> ALL (ARRAY[1, 3])
  GROUP BY (date_part('year'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text)
  ORDER BY (date_part('year'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, ((t3.first_name::text || ' '::text) || t3.last_name::text)
                          """,
        },
    }

    id = Column(BIGINT, primary_key=True)
    service_year = Column(DOUBLE_PRECISION)
    name_ = Column(VARCHAR)
    sales_total = Column(NUMERIC)
    sales_warranty = Column(NUMERIC)
    sales_labour = Column(NUMERIC)
    sales_parts = Column(NUMERIC)
    type_new_installs = Column(NUMERIC)
    type_sales = Column(NUMERIC)
    type_repairs = Column(NUMERIC)
    type_parts = Column(NUMERIC)
    type_prevent_maint = Column(NUMERIC)
    type_rentals = Column(NUMERIC)

    def __repr__(self):
        return f"{self.service_year} - {self.name_} - {self.sales_total}"


class VwSalesByPersonQuarter(Base):
    """Sales by person and quarter (view, not table)"""

    __tablename__ = "vw_sales_by_person_quarter"
    __table_args__ = {
        "schema": "public",
        "info": {
            "view": True,
            "script": r"""
WITH user_count_per_work_order AS (
         SELECT t.work_order_id,
                CASE
                    WHEN t.user_count = 0 THEN 1::bigint
                    ELSE t.user_count
                END AS user_count
           FROM ( SELECT t2_1.id AS work_order_id,
                    count(t31_1.user_id) AS user_count
                   FROM work_orders t2_1
                     LEFT JOIN work_order_user_sales_rel t31_1 ON t31_1.work_order_id = t2_1.id
                  GROUP BY t2_1.id) t
        ), work_orders_parts_cad AS (
         SELECT t1_1.work_order_id,
            t1_1.part_id,
            t2_1.date_service,
            t2_1.service_type_id,
            sum(
                CASE
                    WHEN t2_1.is_warranty IS TRUE THEN 0::numeric
                    ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per
                END) AS cost_before_tax_cad,
            sum(
                CASE
                    WHEN t2_1.is_warranty IS FALSE THEN 0::numeric
                    ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per
                END) AS warranty_cad
           FROM work_orders_parts t1_1
             JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id
             JOIN currencies fx ON fx.id = t2_1.currency_id
          GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id
        )
 SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), (date_part('quarter'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text)) AS id,
    date_part('year'::text, t2.date_service) AS service_year,
    date_part('quarter'::text, t2.date_service) AS service_quarter,
    (t3.first_name::text || ' '::text) || t3.last_name::text AS name_,
    sum(t1.warranty_cad / uc.user_count::numeric) AS sales_warranty,
    sum(t1.cost_before_tax_cad / uc.user_count::numeric) AS sales_total,
    sum(
        CASE
            WHEN t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS sales_labour,
    sum(
        CASE
            WHEN t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS sales_parts,
    sum(
        CASE
            WHEN t5.id = 1 THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS type_new_installs,
    sum(
        CASE
            WHEN t5.id = 4 THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS type_sales,
    sum(
        CASE
            WHEN t5.id = 2 THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS type_repairs,
    sum(
        CASE
            WHEN t5.id = 3 THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS type_parts,
    sum(
        CASE
            WHEN t5.id = 5 THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS type_prevent_maint,
    sum(
        CASE
            WHEN t5.id = 7 THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS type_rentals
   FROM work_orders_parts_cad t1
     JOIN work_orders t2 ON t2.id = t1.work_order_id
     LEFT JOIN work_order_user_sales_rel t31 ON t31.work_order_id = t2.id
     LEFT JOIN public.users t3 ON t3.id = t31.user_id
     JOIN parts t4 ON t4.id = t1.part_id
     JOIN service_types t5 ON t5.id = t2.service_type_id
     JOIN user_count_per_work_order uc ON uc.work_order_id = t2.id
  WHERE t2.customer_id <> ALL (ARRAY[1, 3])
  GROUP BY (date_part('year'::text, t2.date_service)), (date_part('quarter'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text)
  ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('quarter'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, ((t3.first_name::text || ' '::text) || t3.last_name::text)
                            """,
        },
    }

    id = Column(BIGINT, primary_key=True)
    service_year = Column(DOUBLE_PRECISION)
    service_quarter = Column(DOUBLE_PRECISION)
    name_ = Column(VARCHAR)
    sales_total = Column(NUMERIC)
    sales_warranty = Column(NUMERIC)
    sales_labour = Column(NUMERIC)
    sales_parts = Column(NUMERIC)
    type_new_installs = Column(NUMERIC)
    type_sales = Column(NUMERIC)
    type_repairs = Column(NUMERIC)
    type_parts = Column(NUMERIC)
    type_prevent_maint = Column(NUMERIC)
    type_rentals = Column(NUMERIC)

    def __repr__(self):
        return f"{self.service_quarter} - {self.name_} - {self.sales_total}"


class VwSalesByPersonMonth(Base):
    """Sales by person and month (view, not table)"""

    __tablename__ = "vw_sales_by_person_month"
    __table_args__ = {
        "schema": "public",
        "info": {
            "view": True,
            "script": r"""
WITH user_count_per_work_order AS (
         SELECT t.work_order_id,
                CASE
                    WHEN t.user_count = 0 THEN 1::bigint
                    ELSE t.user_count
                END AS user_count
           FROM ( SELECT t2_1.id AS work_order_id,
                    count(t31_1.user_id) AS user_count
                   FROM work_orders t2_1
                     LEFT JOIN work_order_user_sales_rel t31_1 ON t31_1.work_order_id = t2_1.id
                  GROUP BY t2_1.id) t
        ), work_orders_parts_cad AS (
         SELECT t1_1.work_order_id,
            t1_1.part_id,
            t2_1.date_service,
            t2_1.service_type_id,
            sum(
                CASE
                    WHEN t2_1.is_warranty IS TRUE THEN 0::numeric
                    ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per
                END) AS cost_before_tax_cad,
            sum(
                CASE
                    WHEN t2_1.is_warranty IS FALSE THEN 0::numeric
                    ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per
                END) AS warranty_cad
           FROM work_orders_parts t1_1
             JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id
             JOIN currencies fx ON fx.id = t2_1.currency_id
          GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id
        )
 SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text)) AS id,
    date_part('year'::text, t2.date_service) AS service_year,
    date_part('month'::text, t2.date_service) AS service_month,
    (t3.first_name::text || ' '::text) || t3.last_name::text AS name_,
    sum(t1.warranty_cad / uc.user_count::numeric) AS sales_warranty,
    sum(t1.cost_before_tax_cad / uc.user_count::numeric) AS sales_total,
    sum(
        CASE
            WHEN t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS sales_labour,
    sum(
        CASE
            WHEN t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS sales_parts,
    sum(
        CASE
            WHEN t5.id = 1 THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS type_new_installs,
    sum(
        CASE
            WHEN t5.id = 4 THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS type_sales,
    sum(
        CASE
            WHEN t5.id = 2 THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS type_repairs,
    sum(
        CASE
            WHEN t5.id = 3 THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS type_parts,
    sum(
        CASE
            WHEN t5.id = 5 THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS type_prevent_maint,
    sum(
        CASE
            WHEN t5.id = 7 THEN t1.cost_before_tax_cad / uc.user_count::numeric
            ELSE 0::numeric
        END) AS type_rentals
   FROM work_orders_parts_cad t1
     JOIN work_orders t2 ON t2.id = t1.work_order_id
     LEFT JOIN work_order_user_sales_rel t31 ON t31.work_order_id = t2.id
     LEFT JOIN public.users t3 ON t3.id = t31.user_id
     JOIN parts t4 ON t4.id = t1.part_id
     JOIN service_types t5 ON t5.id = t2.service_type_id
     JOIN user_count_per_work_order uc ON uc.work_order_id = t2.id
  WHERE t2.customer_id <> ALL (ARRAY[1, 3])
  GROUP BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text)
  ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('month'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, ((t3.first_name::text || ' '::text) || t3.last_name::text)
                            """,
        },
    }

    id = Column(BIGINT, primary_key=True)
    service_year = Column(DOUBLE_PRECISION)
    service_month = Column(DOUBLE_PRECISION)
    name_ = Column(VARCHAR)
    sales_total = Column(NUMERIC)
    sales_warranty = Column(NUMERIC)
    sales_labour = Column(NUMERIC)
    sales_parts = Column(NUMERIC)
    type_new_installs = Column(NUMERIC)
    type_sales = Column(NUMERIC)
    type_repairs = Column(NUMERIC)
    type_parts = Column(NUMERIC)
    type_prevent_maint = Column(NUMERIC)
    type_rentals = Column(NUMERIC)

    def __repr__(self):
        return f"{self.service_year}-{self.service_month} - {self.name_} - {self.sales_total}"


class MaintenanceType(Base):
    """Maintenance types"""

    __tablename__ = "maintenance_types"
    __table_args__ = {
        "schema": "public",
    }

    id = Column(INTEGER, primary_key=True)
    name = Column(VARCHAR, nullable=False)
    description = Column(TEXT, nullable=True)

    maintenance_rel = relationship("Maintenance", back_populates="maintenance_type_rel")

    def __repr__(self):
        return self.name


class Maintenance(Base):
    """Maintenance records (when the user clicks the 'preventative maintenance reset' button)"""

    __tablename__ = "maintenance"
    __table_args__ = {
        "schema": "public",
    }

    id = Column(INTEGER, primary_key=True)
    timestamp_utc = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    description = Column(TEXT, nullable=True)
    op_hours = Column(REAL, nullable=True)
    # Calculated field, based on op_hours
    op_months = Column(REAL, Computed("op_hours / 24"))

    maintenance_type_id = Column(
        INTEGER,
        ForeignKey("public.maintenance_types.id", ondelete="CASCADE"),
        nullable=False,
    )
    maintenance_type_rel = relationship(
        "MaintenanceType", back_populates="maintenance_rel"
    )

    structure_id = Column(
        INTEGER,
        ForeignKey("public.structures.id", ondelete="CASCADE"),
    )
    structures_rel = relationship("Structure", back_populates="maintenance_rel")

    power_unit_id = Column(INTEGER, ForeignKey("public.power_units.id"), nullable=True)
    power_units_rel = relationship("PowerUnit", back_populates="maintenance_rel")

    user_id = Column(INTEGER, ForeignKey("public.users.id"), nullable=True)
    users_rel = relationship("User", back_populates="maintenance_rel")
