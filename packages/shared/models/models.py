import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Optional

import pytz
from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    Check<PERSON>onstraint,
    Column,
    Computed,
    ForeignKey,
    Index,
    String,
    Table,
    UniqueConstraint,
    func,
)
from sqlalchemy.dialects.postgresql import (
    ARRAY,
    BIGINT,
    BOOLEAN,
    BYTEA,
    CHAR,
    DATE,
    DOUBLE_PRECISION,
    FLOAT,
    INTEGER,
    INTERVAL,
    JSON,
    JSONB,
    NUMERIC,
    REAL,
    SMALLINT,
    TEXT,
    TIMESTAMP,
    UUID,
    VARCHAR,
)
from sqlalchemy.ext.associationproxy import association_proxy
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import relationship

from shared.config import (
    COUNTRY_ID_CANADA,
    COUNTRY_ID_USA,
    CUSTOMER_ID_DEMO,
    GATEWAY_TYPE_ID_AXIOMTEK,
    IJACK_CUST_IDS_LIST,
    PROVINCE_ID_SK,
    ROLE_ID_CUSTOMER,
    TIME_ZONE_ID_AMERICA_REGINA,
    USER_ID_SEAN,
)
from shared.models.base import Base, metadata
from shared.models.models_work_order import (
    work_order_power_unit_rel,
    work_order_structure_rel,
    work_order_unit_type_rel,
    work_order_user_rel,
    work_order_user_sales_rel,
)
from shared.utils.datetime_utils import utcnow_naive
from shared.utils.formatters import remove_unnecessary_decimal

# many-to-many relationship between User and Role classes/tables
user_role_rel = Table(
    "user_role_rel",
    metadata,
    Column(
        "user_id",
        INTEGER,
        ForeignKey("public.users.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "role_id",
        INTEGER,
        ForeignKey("public.roles.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
        default=ROLE_ID_CUSTOMER,
    ),
    schema="public",
)

# many-to-many relationship between User and Customer classes/tables
user_customer_rel = Table(
    "user_customer_rel",
    metadata,
    Column(
        "user_id",
        INTEGER,
        ForeignKey("public.users.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "customer_id",
        INTEGER,
        ForeignKey("public.customers.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# many-to-many relationship (remote control permissions) between User and Structure classes/tables [controlling units at the gateway (not CustSubGroup) level]
# NOTE this includes operators and maintenance contacts for DGAS units without power units/gateways/RCOM
user_structure_remote_control_rel = Table(
    "user_structure_remote_control_rel",
    metadata,
    Column(
        "user_id",
        INTEGER,
        ForeignKey("public.users.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "structure_id",
        INTEGER,
        ForeignKey("public.structures.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

user_structure_maintenance_rel = Table(
    "user_structure_maintenance_rel",
    metadata,
    Column(
        "user_id",
        INTEGER,
        ForeignKey("public.users.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "structure_id",
        INTEGER,
        ForeignKey("public.structures.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

user_structure_operators_rel = Table(
    "user_structure_operators_rel",
    metadata,
    Column(
        "user_id",
        INTEGER,
        ForeignKey("public.users.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "structure_id",
        INTEGER,
        ForeignKey("public.structures.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

user_structure_sales_rel = Table(
    "user_structure_sales_rel",
    metadata,
    Column(
        "user_id",
        INTEGER,
        ForeignKey("public.users.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "structure_id",
        INTEGER,
        ForeignKey("public.structures.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# many-to-many relationship between Structure and Customer classes/tables (to allow several customers to co-own a structure)
structure_customer_rel = Table(
    "structure_customer_rel",
    metadata,
    Column(
        "structure_id",
        INTEGER,
        ForeignKey("public.structures.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "customer_id",
        INTEGER,
        ForeignKey("public.customers.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# many-to-many relationship between Structure and CustSubGroup classes/tables
# (to allow a structure to be part of several customer sub-groups)
structure_cust_sub_group_rel = Table(
    "structure_cust_sub_group_rel",
    metadata,
    Column(
        "structure_id",
        INTEGER,
        ForeignKey("public.structures.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "cust_sub_group_id",
        INTEGER,
        ForeignKey("public.cust_sub_groups.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# # many-to-many relationship between User and CustSubGroup classes/tables (for what purpose???)
# user_cust_sub_group_rel = Table(
#     "user_cust_sub_group_rel",
#     metadata,
#     Column(
#         "user_id",
#         INTEGER,
#         ForeignKey("public.users.id", ondelete="CASCADE"),
#         primary_key=True,
#         nullable=False,
#     ),
#     Column(
#         "cust_sub_group_id",
#         INTEGER,
#         ForeignKey("public.cust_sub_groups.id", ondelete="CASCADE"),
#         primary_key=True,
#         nullable=False,
#     ),
#     schema="public",
# )

# many-to-many relationship between User and CustSubGroup classes/tables,
# for which they want notifications of new service requests.
# Similar to the "notify_service_requests" field in the User table.
user_cust_sub_group_notify_service_requests_rel = Table(
    "user_cust_sub_group_notify_service_requests_rel",
    metadata,
    Column(
        "user_id",
        INTEGER,
        ForeignKey("public.users.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "cust_sub_group_id",
        INTEGER,
        ForeignKey("public.cust_sub_groups.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# many-to-many relationship between ReportEmailHourly and ModelType classes/tables
report_email_hourly_model_types_rel = Table(
    "report_email_hourly_model_types_rel",
    metadata,
    Column(
        "report_id",
        INTEGER,
        ForeignKey("public.report_email_hourly.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "model_type_id",
        INTEGER,
        ForeignKey("public.model_types.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# many-to-many relationship between ReportEmailDerates and ModelType classes/tables
report_email_derates_model_types_rel = Table(
    "report_email_derates_model_types_rel",
    metadata,
    Column(
        "report_id",
        INTEGER,
        ForeignKey("public.report_email_derates.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "model_type_id",
        INTEGER,
        ForeignKey("public.model_types.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# Many-to-many relationship between ReportEmailOpHours and ModelType classes/tables
report_email_op_hours_model_types_rel = Table(
    "report_email_op_hours_model_types_rel",
    metadata,
    Column(
        "report_id",
        INTEGER,
        ForeignKey("public.report_email_op_hours.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "model_type_id",
        INTEGER,
        ForeignKey("public.model_types.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# many-to-many relationship between ReportEmailHourly and UnitType classes/tables
report_email_hourly_unit_types_rel = Table(
    "report_email_hourly_unit_types_rel",
    metadata,
    Column(
        "report_id",
        INTEGER,
        ForeignKey("public.report_email_hourly.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "unit_type_id",
        INTEGER,
        ForeignKey("public.unit_types.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# many-to-many relationship between ReportEmailDerates and UnitType classes/tables
report_email_derates_unit_types_rel = Table(
    "report_email_derates_unit_types_rel",
    metadata,
    Column(
        "report_id",
        INTEGER,
        ForeignKey("public.report_email_derates.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "unit_type_id",
        INTEGER,
        ForeignKey("public.unit_types.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# many-to-many relationship between ReportEmailOpHours and UnitType classes/tables
report_email_op_hours_unit_types_rel = Table(
    "report_email_op_hours_unit_types_rel",
    metadata,
    Column(
        "report_id",
        INTEGER,
        ForeignKey("public.report_email_op_hours.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "unit_type_id",
        INTEGER,
        ForeignKey("public.unit_types.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# many-to-many relationship between ReportEmailHourly and Hour classes/tables
report_email_hourly_hours_rel = Table(
    "report_email_hourly_hours_rel",
    metadata,
    Column(
        "report_id",
        INTEGER,
        ForeignKey("public.report_email_hourly.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "hour",
        INTEGER,
        ForeignKey("public.hours.hour"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# many-to-many relationship between ReportEmailInventory and Hour classes/tables
report_email_inventory_hours_rel = Table(
    "report_email_inventory_hours_rel",
    metadata,
    Column(
        "report_id",
        INTEGER,
        ForeignKey("public.report_email_inventory.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "hour",
        INTEGER,
        ForeignKey("public.hours.hour"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# many-to-many relationship between ReportEmailDerates and Hour classes/tables
report_email_derates_hours_rel = Table(
    "report_email_derates_hours_rel",
    metadata,
    Column(
        "report_id",
        INTEGER,
        ForeignKey("public.report_email_derates.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "hour",
        INTEGER,
        ForeignKey("public.hours.hour"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# Many-to-many relationship between ReportEmailHourly and DayOfWeek classes/tables
report_email_hourly_days_of_week_rel = Table(
    "report_email_hourly_days_of_week_rel",
    metadata,
    Column(
        "report_id",
        INTEGER,
        ForeignKey("public.report_email_hourly.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "day_of_week_id",
        INTEGER,
        ForeignKey("public.days_of_week.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# Many-to-many relationship between ReportEmailDerates and DayOfWeek classes/tables
report_email_derates_days_of_week_rel = Table(
    "report_email_derates_days_of_week_rel",
    metadata,
    Column(
        "report_id",
        INTEGER,
        ForeignKey("public.report_email_derates.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "day_of_week_id",
        INTEGER,
        ForeignKey("public.days_of_week.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# Many-to-many relationship between ReportEmailInventory and DayOfWeek classes/tables
report_email_inventory_days_of_week_rel = Table(
    "report_email_inventory_days_of_week_rel",
    metadata,
    Column(
        "report_id",
        INTEGER,
        ForeignKey("public.report_email_inventory.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "day_of_week_id",
        INTEGER,
        ForeignKey("public.days_of_week.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# Many-to-many relationship between ReportEmailOpHours and ReportEmailOpHoursType
report_email_op_hours_types_rel = Table(
    "report_email_op_hours_types_rel",
    metadata,
    Column(
        "report_id",
        INTEGER,
        ForeignKey("public.report_email_op_hours.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "report_email_op_hours_type_id",
        INTEGER,
        ForeignKey("public.report_email_op_hours_types.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# Many-to-many relationship between ReportEmailHourly and Warehouse classes/tables
report_email_hourly_warehouses_rel = Table(
    "report_email_hourly_warehouses_rel",
    metadata,
    Column(
        "report_id",
        INTEGER,
        ForeignKey("public.report_email_hourly.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "warehouse_id",
        INTEGER,
        ForeignKey("public.warehouses.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# Many-to-many relationship between ReportEmailDerates and Warehouse classes/tables
report_email_derates_warehouses_rel = Table(
    "report_email_derates_warehouses_rel",
    metadata,
    Column(
        "report_id",
        INTEGER,
        ForeignKey("public.report_email_derates.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "warehouse_id",
        INTEGER,
        ForeignKey("public.warehouses.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# Many-to-many relationship between ReportEmailOpHours and Warehouse classes/tables
report_email_op_hours_warehouses_rel = Table(
    "report_email_op_hours_warehouses_rel",
    metadata,
    Column(
        "report_id",
        INTEGER,
        ForeignKey("public.report_email_op_hours.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "warehouse_id",
        INTEGER,
        ForeignKey("public.warehouses.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

report_email_inventory_warehouses_rel = Table(
    "report_email_inventory_warehouses_rel",
    metadata,
    Column(
        "report_id",
        INTEGER,
        ForeignKey("public.report_email_inventory.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "warehouse_id",
        INTEGER,
        ForeignKey("public.warehouses.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# View of one-to-many relationship between Gw and Structure classes/tables
vw_gw_structure_rel = Table(
    "vw_gw_structure_rel",
    metadata,
    Column(
        "gw",
        INTEGER,
        ForeignKey("public.gw.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "power_units",
        INTEGER,
        ForeignKey("public.power_units.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "structures",
        INTEGER,
        ForeignKey("public.structures.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
    info={
        "view": True,
        "script": r"""
                    SELECT DISTINCT t1.id AS gw,
                        t2.id AS power_units,
                        t3.id AS structures
                    FROM gw t1
                        LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id
                        LEFT JOIN structures t3 ON t3.power_unit_id = t2.id
                    WHERE t3.id IS NOT NULL
                    ORDER BY t1.id
                    """,
    },
)

# View of many-to-many relationship between Gw and Customer classes/tables
vw_gw_customer_rel = Table(
    "vw_gw_customer_rel",
    metadata,
    Column("gw", INTEGER, ForeignKey("public.gw.id"), primary_key=True, nullable=False),
    Column(
        "power_units",
        INTEGER,
        ForeignKey("public.power_units.id"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "structures",
        INTEGER,
        ForeignKey("public.structures.id"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "customers",
        INTEGER,
        ForeignKey("public.customers.id"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
    info={
        "view": True,
        "script": r"""
                    SELECT DISTINCT t1.id AS gw,
                        t2.id AS power_units,
                        t3.id AS structures,
                        t5.id AS customers
                    FROM gw t1
                        LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id
                        LEFT JOIN structures t3 ON t3.power_unit_id = t2.id
                        LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t3.id
                        LEFT JOIN public.customers t5 ON t5.id = t4.customer_id
                    WHERE t5.id IS NOT NULL
                    ORDER BY t1.id
                    """,
    },
)
# View of many-to-many relationship between Gw and CustSubGroup classes/tables
vw_gw_cust_sub_group_rel = Table(
    "vw_gw_cust_sub_group_rel",
    metadata,
    Column(
        "gw",
        INTEGER,
        ForeignKey("public.gw.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "power_units",
        INTEGER,
        ForeignKey("public.power_units.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "structures",
        INTEGER,
        ForeignKey("public.structures.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "cust_sub_groups",
        INTEGER,
        ForeignKey("public.cust_sub_groups.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
    info={
        "view": True,
        "script": r"""
                    SELECT DISTINCT t1.id AS gw,
                        t2.id AS power_units,
                        t3.id AS structures,
                        t6.id AS cust_sub_groups
                    FROM gw t1
                        LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id
                        LEFT JOIN structures t3 ON t3.power_unit_id = t2.id
                        LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t3.id
                        left join public.structure_cust_sub_group_rel csr ON csr.structure_id = t3.id
                        LEFT JOIN public.cust_sub_groups t6 ON t6.id = csr.cust_sub_group_id
                    WHERE t6.id IS NOT NULL
                    ORDER BY t1.id
                    """,
    },
)

# many-to-many relationship between User and AlertCustom classes/tables
alerts_custom_user_rel = Table(
    "alerts_custom_user_rel",
    metadata,
    Column(
        "alerts_custom_id",
        INTEGER,
        ForeignKey("public.alerts_custom.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "user_id",
        INTEGER,
        ForeignKey("public.users.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)


# many-to-many relationship between Structure and AlertCustom classes/tables
alerts_custom_structure_rel = Table(
    "alerts_custom_structure_rel",
    metadata,
    Column(
        "alerts_custom_id",
        INTEGER,
        ForeignKey("public.alerts_custom.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "structure_id",
        INTEGER,
        ForeignKey("public.structures.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# many-to-many relationship between AlertCustom and Month classes/tables
alerts_custom_months_rel = Table(
    "alerts_custom_months_rel",
    metadata,
    Column(
        "alerts_custom_id",
        INTEGER,
        ForeignKey("public.alerts_custom.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "month",
        SMALLINT,
        ForeignKey("public.months.month"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# many-to-many relationship between AlertCustom and Day classes/tables
alerts_custom_days_rel = Table(
    "alerts_custom_days_rel",
    metadata,
    Column(
        "alerts_custom_id",
        INTEGER,
        ForeignKey("public.alerts_custom.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "day",
        SMALLINT,
        ForeignKey("public.days.day"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)

# many-to-many relationship between AlertCustom and Month classes/tables
alerts_custom_images_rel = Table(
    "alerts_custom_images_rel",
    metadata,
    Column(
        "alerts_custom_id",
        INTEGER,
        ForeignKey("public.alerts_custom.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "image_id",
        INTEGER,
        ForeignKey("public.images.id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
    ),
    schema="public",
)


def get_string_rep(string):
    """Remove certain characters from the string, for Flask-Admin formatting"""
    return str(string).replace("'", "").replace("[", "").replace("]", "")


class User(Base):
    """
    Create a User table Mixin
    """

    # Ensures table will be named in plural and not in singular
    # as is the name of the model
    __abstract__ = True
    __tablename__ = "users"
    __table_args__ = {"schema": "public"}

    @property
    def is_active(self):
        """
        Override the default is_active property from UserMixin.
        This works by returning the value of the is_active column in the database,
        instead of the property itself.
        """
        return self.is_active

    @is_active.setter
    def is_active(self, value):
        """
        Override the default is_active setter from UserMixin.
        This works by setting the value of the is_active column in the database,
        instead of the property itself.
        """
        self.is_active = value

    id = Column(INTEGER, primary_key=True)
    date_created = Column(DATE, default=utcnow_naive, nullable=False)
    email = Column(String(60), unique=True, nullable=False)
    # username = Column(String(60), unique=True)
    first_name = Column(String(60), nullable=False)
    last_name = Column(String(60), nullable=False)
    full_name = Column(VARCHAR, Computed("first_name || ' ' || last_name"))
    # password_hash = Column(String(128), nullable=False)
    password_hash = Column(VARCHAR, nullable=False)

    # CREATE EXTENSION IF NOT EXISTS "pgcrypto";
    # ALTER TABLE public.users ADD COLUMN uuid UUID DEFAULT gen_random_uuid() NOT NULL;
    uuid = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, nullable=False)

    # FastAPI needs is_active and is_superuser to be defined as properties.
    # Instead of deleting users, we just set them to "inactive" so they can't log in.
    # This way their data is still available for reporting, etc. Like work orders created
    # by a user who has left the company.

    is_active = Column(BOOLEAN, default=True, nullable=False)
    is_superuser = Column(BOOLEAN, default=False)

    # Voluntary data
    # IJACK Shop GPS latitude == 50.1631 and longitude == -101.6754
    user_lat = Column(DOUBLE_PRECISION, default=50.1631, nullable=False)
    user_lon = Column(DOUBLE_PRECISION, default=-101.6754, nullable=False)
    company = Column(String(60), default=None)
    job_title = Column(VARCHAR, default=None)
    city = Column(String(60), default=None)

    country_id = Column(
        INTEGER,
        ForeignKey("public.countries.id"),
        nullable=False,
        default=COUNTRY_ID_CANADA,
    )

    @declared_attr
    def countries_rel(self):
        return relationship(
            "Country", back_populates="users_rel", foreign_keys=[self.country_id]
        )

    # Main customer
    # NOTE: the following foreign key is circular with the Customer.accounting_contact_id
    # so before we can drop the table, we first need to run connection.execute(text("SET CONSTRAINTS ALL DEFERRED"))
    customer_id = Column(
        INTEGER,
        ForeignKey(
            "public.customers.id",
            use_alter=True,
            name="users_fk_customer_id",
            deferrable=True,
            initially="DEFERRED",
        ),
        nullable=False,
        default=CUSTOMER_ID_DEMO,
    )

    @declared_attr
    def main_customer_rel(self):
        return relationship(
            "Customer",
            back_populates="main_users_rel",
            foreign_keys=[self.customer_id],
        )

    # Many-to-many relationships
    # Other customers the user can see
    @declared_attr
    def customers_rel(self):
        return relationship(
            "Customer",
            secondary=user_customer_rel,
            back_populates="users_rel",
            # Always load the user's customer right away?
            # lazy="joined",
        )

    @declared_attr
    def career_applications_rel(self):
        return relationship(
            "CareerApplication",
            back_populates="users_rel",
            cascade="all, delete-orphan",
            passive_deletes=True,
        )

    # cust_sub_groups_rel = relationship(
    #     "CustSubGroup", secondary=user_cust_sub_group_rel, back_populates="users_rel"
    # )
    # This one is for service requests - each user can see many customer sub-groups,
    # and each customer sub-group can be seen by many users
    @declared_attr
    def cust_sub_groups_notify_service_requests_rel(self):
        return relationship(
            "CustSubGroup",
            secondary=user_cust_sub_group_notify_service_requests_rel,
            back_populates="users_notify_service_requests_rel",
        )

    # This one is for remote control - each user can control many structures,
    # and each structure can be controlled by many users
    @declared_attr
    def structures_rel(self):
        return relationship(
            "Structure",
            secondary=user_structure_remote_control_rel,
            back_populates="users_rel",
            lazy="subquery",
        )

    # power_unit_str = association_proxy("structures", "power_unit_str")
    # structure = association_proxy("structures", "structure")

    @declared_attr
    def structures_operators_rel(self):
        return relationship(
            "Structure",
            secondary=user_structure_operators_rel,
            back_populates="operators_users_rel",
        )

    @declared_attr
    def maintenance_structures_rel(self):
        return relationship(
            "Structure",
            secondary=user_structure_maintenance_rel,
            back_populates="maintenance_users_rel",
        )

    @declared_attr
    def sales_structures_rel(self):
        return relationship(
            "Structure",
            secondary=user_structure_sales_rel,
            back_populates="sales_users_rel",
        )

    @declared_attr
    def roles_rel(self):
        return relationship(
            "Role",
            secondary=user_role_rel,
            back_populates="users_rel",
            # default=[ROLE_ID_SELF_REGISTERED],
            # Always load the user's roles right away?
            # lazy="joined",
        )

    # We don't use this anymore, I think, since it's now unit-by-unit for permissions
    # can_set = Column(BOOLEAN, default=True)

    phone = Column(String(18), unique=True)
    # If sending to US phone number, use US Twilio phone number instead of Canadian number
    is_us_phone = Column(BOOLEAN, default=False, nullable=False)

    time_zone_id = Column(
        INTEGER,
        ForeignKey("public.time_zones.id"),
        default=TIME_ZONE_ID_AMERICA_REGINA,
        nullable=False,
    )

    @declared_attr
    def time_zones_rel(self):
        return relationship(
            "TimeZone",
            back_populates="users_rel",
            # default=TIME_ZONE_ID_AMERICA_REGINA
            # info={"default": TIME_ZONE_ID_AMERICA_REGINA},
        )

    # New user email confirmation
    is_confirmed = Column(BOOLEAN, default=False, nullable=False)
    confirmed_at = Column(TIMESTAMP, nullable=True)

    # SMS compliance for spam filtering
    sms_stop_all = Column(BOOLEAN, default=False, nullable=False)
    sms_confirmed_at_utc = Column(TIMESTAMP, nullable=True)
    sms_stopped_at_utc = Column(TIMESTAMP, nullable=True)

    # WhatsApp compliance for spam filtering
    whatsapp_stop_all = Column(BOOLEAN, default=False, nullable=False)
    whatsapp_confirmed_at_utc = Column(TIMESTAMP, nullable=True)
    whatsapp_stopped_at_utc = Column(TIMESTAMP, nullable=True)

    # alerts_enabled = Column(BOOLEAN, default=True, nullable=False)
    alerts_paused_until_utc = Column(TIMESTAMP)

    @declared_attr
    def alerts_rel(self):
        return relationship(
            "Alert",
            back_populates="users_rel",
            cascade="all, delete-orphan",
            passive_deletes=True,
        )

    @declared_attr
    def alerts_custom_rel(self):
        return relationship(
            "AlertCustom",
            secondary=alerts_custom_user_rel,
            back_populates="users_rel",
        )

    @declared_attr
    def alerts_sent_users_rel(self):
        return relationship("AlertsSentUser", back_populates="users_rel")

    @declared_attr
    def alerts_sent_maint_users_rel(self):
        return relationship(
            "AlertsSentMaintUser",
            back_populates="users_rel",
            cascade="all, delete-orphan",
            passive_deletes=True,
        )

    @declared_attr
    def career_files_rel(self):
        return relationship(
            "CareerFile",
            back_populates="users_rel",
            cascade="all, delete-orphan",
            passive_deletes=True,
        )

    # records when a user submits a remote control command via the website

    @declared_attr
    def remote_control_rel(self):
        return relationship(
            "RemoteControl",
            back_populates="users_rel",
            cascade="all, delete-orphan",
            passive_deletes=True,
        )

    @declared_attr
    def website_views_rel(self):
        return relationship(
            "WebsiteView",
            back_populates="users_rel",
            cascade="all, delete-orphan",
            passive_deletes=True,
        )

    @declared_attr
    def service_emailees_rel(self):
        return relationship(
            "ServiceEmailee",
            back_populates="user_rel",
            cascade="all, delete-orphan",
            passive_deletes=True,
        )

    @declared_attr
    def work_orders_rel(self):
        return relationship(
            "WorkOrder",
            secondary=work_order_user_rel,
            back_populates="users_rel",
            # https://docs.sqlalchemy.org/en/14/orm/cascades.html#using-delete-cascade-with-many-to-many-relationships
            # Only delete the record in the association table, NOT the related record!
            cascade="all, delete",
            passive_deletes=True,
        )

    @declared_attr
    def work_orders_sales_rel(self):
        return relationship(
            "WorkOrder",
            secondary=work_order_user_sales_rel,
            back_populates="users_sales_rel",
            # https://docs.sqlalchemy.org/en/14/orm/cascades.html#using-delete-cascade-with-many-to-many-relationships
            # Only delete the record in the association table, NOT the related record!
            cascade="all, delete",
            passive_deletes=True,
        )

    @declared_attr
    def work_orders_creator_rel(self):
        return relationship(
            "WorkOrder",
            back_populates="creator_rel",
            foreign_keys="[WorkOrder.creator_id]",
        )

    @declared_attr
    def work_orders_requested_by_rel(self):
        return relationship(
            "WorkOrder",
            back_populates="requested_by_rel",
            foreign_keys="[WorkOrder.requested_by_id]",
        )

    @declared_attr
    def work_orders_approval_person_rel(self):
        return relationship(
            "WorkOrder",
            back_populates="approval_person_rel",
            foreign_keys="[WorkOrder.approval_person_id]",
        )

    @declared_attr
    def work_orders_approved_by_rel(self):
        return relationship(
            "WorkOrder",
            back_populates="approved_by_rel",
            foreign_keys="[WorkOrder.approved_by_id]",
        )

    @declared_attr
    def user_api_tokens_rel(self):
        return relationship("UserAPIToken", back_populates="users_rel")

    @declared_attr
    def ledger_entries_rel(self):
        return relationship("InventoryLedger", back_populates="created_by_rel")

    # work_order_parts_sales_person_rel = relationship(
    #     "WorkOrderPart",
    #     back_populates="sales_person_rel",
    #     foreign_keys="[WorkOrderPart.sales_person_id]",
    # )

    @declared_attr
    def work_order_parts_field_tech_rel(self):
        return relationship(
            "WorkOrderPart",
            back_populates="field_tech_rel",
            foreign_keys="[WorkOrderPart.field_tech_id]",
        )

    @declared_attr
    def service_rel(self):
        return relationship("Service", back_populates="users_rel")

    @declared_attr
    def service_clock_rel(self):
        return relationship("ServiceClock", back_populates="employee_rel")

    @declared_attr
    def applications_rel(self):
        return relationship("Application", back_populates="user_rel")

    @declared_attr
    def maintenance_rel(self):
        return relationship("Maintenance", back_populates="users_rel")

    @declared_attr
    def contact_form_rel(self):
        return relationship("ContactForm", back_populates="user_rel")

    @declared_attr
    def profiler_measurements_rel(self):
        return relationship("Measurement", back_populates="user_rel")

    @declared_attr
    def calories_rel(self):
        return relationship("Calorie", back_populates="user_rel")

    @declared_attr
    def report_email_hourly_rel(self):
        return relationship("ReportEmailHourly", back_populates="users_rel")

    @declared_attr
    def report_email_derates_rel(self):
        return relationship("ReportEmailDerates", back_populates="users_rel")

    @declared_attr
    def report_email_op_hours_rel(self):
        return relationship("ReportEmailOpHours", back_populates="user_rel")

    @declared_attr
    def report_email_inventory_rel(self):
        return relationship("ReportEmailInventory", back_populates="user_rel")

    @declared_attr
    def oath_rel(self):
        return relationship("OAuth", back_populates="user_rel")

    @declared_attr
    def webauthn_credentials_rel(self):
        return relationship("WebauthnCredential", back_populates="user_rel")

    @declared_attr
    def verification_codes_rel(self):
        return relationship("UserVerificationCode", back_populates="user_rel")

    @declared_attr
    def registration_challenges_rel(self):
        return relationship("UserRegistrationChallenge", back_populates="user_rel")

    @declared_attr
    def authentication_challenges_rel(self):
        return relationship("UserAuthenticationChallenge", back_populates="user_rel")

    @declared_attr
    def error_logs(self):
        return relationship(
            "ErrorLog", back_populates="user_rel", foreign_keys="[ErrorLog.user_id]"
        )

    @declared_attr
    def resolved_errors(self):
        return relationship(
            "ErrorLog",
            back_populates="resolved_by_user",
            foreign_keys="[ErrorLog.resolved_by_user_id]",
        )

    @declared_attr
    def chart_preferences_rel(self):
        return relationship("UserChartPreference", back_populates="user_rel")

    @declared_attr
    def chart_toggles_rel(self):
        return relationship("UserChartToggle", back_populates="user_rel")

    @declared_attr
    def accounting_contact_customers_rel(self):
        return relationship(
            "Customer",
            back_populates="accounting_contact_rel",
            foreign_keys="[Customer.accounting_contact_id]",
        )

    current_login_at = Column(TIMESTAMP)
    prev_login_at = Column(TIMESTAMP)

    current_login_ip = Column(VARCHAR)
    last_login_ip = Column(VARCHAR)
    login_count = Column(INTEGER)

    # Gets set every time the user makes a request (not more frequently than every 60 seconds though)
    last_seen_at = Column(TIMESTAMP)
    last_error_at = Column(TIMESTAMP)

    # Email marketing campaigns
    eml_unsubscribe_all = Column(BOOLEAN, default=False)
    eml_marketing = Column(BOOLEAN, default=True)
    eml_new_products = Column(BOOLEAN, default=True)
    eml_service = Column(BOOLEAN, default=True)
    eml_rcom = Column(BOOLEAN, default=True)

    # To keep record of when the user has unsubscribed from various email types
    eml_unsubscribe_all_stopped_at_utc = Column(TIMESTAMP, nullable=True)
    eml_marketing_stopped_at_utc = Column(TIMESTAMP, nullable=True)
    eml_new_products_stopped_at_utc = Column(TIMESTAMP, nullable=True)
    eml_service_stopped_at_utc = Column(TIMESTAMP, nullable=True)
    eml_rcom_stopped_at_utc = Column(TIMESTAMP, nullable=True)

    # Wants to be notified (CC'd) when service requests are submitted
    notify_service_requests = Column(BOOLEAN, default=False)

    def get_user_cust_ids(self) -> tuple:
        """Return a list of the user's customer IDs"""
        cust_ids_set: set = set([self.customer_id])
        for customer in self.customers_rel:
            cust_ids_set.add(customer.id)
        return tuple(cust_ids_set)

    def user_in_cust_ids_list(self, customer_ids_list: List[int]) -> bool:
        """Is the user in the list of customers?"""
        user_cust_ids: tuple = self.get_user_cust_ids()
        for cust_id in user_cust_ids:
            if cust_id in customer_ids_list:
                return True
        return False

    def user_is_ijack_employee(self) -> bool:
        """Is the user an IJACK employee?"""
        return self.user_in_cust_ids_list(IJACK_CUST_IDS_LIST)

    def has_role_id(self, role_id: int) -> bool:
        """Does the user have the specified role?"""
        for role in self.roles_rel:
            if role.id == role_id:
                return True
        return False

    def __repr__(self):
        # return '<User: {}>'.format(self.username)
        return f"{self.first_name} {self.last_name} ({self.email})"

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.email})"


class UserAPIToken(Base):
    """
    Table for storing API tokens, for users to access the APIs
    """

    __tablename__ = "user_api_tokens"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    name = Column(VARCHAR)
    description = Column(TEXT)

    def expiry_default(self, days: int = 365):
        """Default expiry date for the token"""
        return datetime.today() + timedelta(days=days)

    expires = Column(DATE, nullable=False, default=expiry_default)

    # For the API, to store the latest bearer token the user has created
    token = Column(VARCHAR, nullable=False)

    user_id = Column(
        INTEGER,
        ForeignKey("public.users.id", ondelete="CASCADE"),
        nullable=False,
    )
    # users_rel = relationship("User", secondary=user_role_rel, back_populates="roles_rel")
    users_rel = relationship("User", back_populates="user_api_tokens_rel")
    # users_rel_first_name = association_proxy("user", "first_name")
    # user_last_name = association_proxy("user", "last_name")
    # user_email = association_proxy("user", "email")

    def __repr__(self):
        return f"{self.users_rel} - {self.token}"


class UserVerificationCode(Base):
    """
    Table for storing verification codes for user email verification
    """

    __tablename__ = "user_verification_codes"
    __table_args__ = (
        CheckConstraint(
            "code_expiry_utc > code_sent_at_utc",
            name="code_expiry_utc_gt_code_sent_at_utc",
        ),
        UniqueConstraint("user_id", name="user_id_code_unique_constraint"),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)
    user_id = Column(
        INTEGER,
        ForeignKey("public.users.id", ondelete="CASCADE"),
        nullable=False,
    )
    user_rel = relationship("User", back_populates="verification_codes_rel")

    code = Column(INTEGER, nullable=False)
    code_sent_at_utc = Column(TIMESTAMP, nullable=False)
    code_expiry_utc = Column(TIMESTAMP, nullable=False)

    def __repr__(self):
        return f"{self.user_rel} - {self.code}"


class UserRegistrationChallenge(Base):
    """
    Table for storing registration challenges for user email verification
    """

    __tablename__ = "user_registration_challenges"
    __table_args__ = (
        CheckConstraint(
            "challenge_expiry_utc > challenge_sent_at_utc",
            name="challenge_expiry_utc_gt_challenge_sent_at_utc",
        ),
        UniqueConstraint("user_id"),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)
    user_id = Column(
        INTEGER,
        ForeignKey("public.users.id", ondelete="CASCADE"),
        nullable=False,
    )
    user_rel = relationship("User", back_populates="registration_challenges_rel")

    challenge = Column(BYTEA, nullable=False)
    challenge_sent_at_utc = Column(TIMESTAMP, nullable=False)
    challenge_expiry_utc = Column(TIMESTAMP, nullable=False)

    def __repr__(self):
        return f"{self.user_rel} - {self.challenge}"


class UserAuthenticationChallenge(Base):
    """
    Table for storing authentication challenges for user email verification
    """

    __tablename__ = "user_authentication_challenges"
    __table_args__ = (
        CheckConstraint(
            "challenge_expiry_utc > challenge_sent_at_utc",
            name="challenge_expiry_utc_gt_challenge_sent_at_utc",
        ),
        UniqueConstraint("user_id"),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)
    user_id = Column(
        INTEGER,
        ForeignKey("public.users.id", ondelete="CASCADE"),
        nullable=False,
    )
    user_rel = relationship("User", back_populates="authentication_challenges_rel")

    challenge = Column(BYTEA, nullable=False)
    challenge_sent_at_utc = Column(TIMESTAMP, nullable=False)
    challenge_expiry_utc = Column(TIMESTAMP, nullable=False)

    def __repr__(self):
        return f"{self.user_rel} - {self.challenge}"


class WebauthnCredential(Base):
    """
    Table for storing WebAuthn credentials (i.e. PassKeys)
    """

    __tablename__ = "user_webauthn_credentials"
    __table_args__ = (
        UniqueConstraint(
            "user_id", "rp_id", name="user_id_credential_id_unique_constraint"
        ),
        {"schema": "public"},
    )

    # id = Column(INTEGER, primary_key=True)
    # CREATE EXTENSION IF NOT EXISTS "pgcrypto";
    # ALTER TABLE public.users ADD COLUMN uuid UUID DEFAULT gen_random_uuid() NOT NULL;
    # id = Column(
    #     UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    # )
    # id = Column(
    #     BYTEA, primary_key=True
    # )
    credential_id = Column(BYTEA, nullable=False, primary_key=True)
    user_id = Column(
        INTEGER,
        ForeignKey("public.users.id", ondelete="CASCADE"),
        nullable=False,
    )
    user_rel = relationship("User", back_populates="webauthn_credentials_rel")

    credential_public_key = Column(BYTEA, nullable=False)
    sign_count = Column(INTEGER, nullable=False, default=0)
    transports = Column(ARRAY(VARCHAR), nullable=True)

    rp_id = Column(VARCHAR, nullable=True)
    user_handle = Column(VARCHAR, nullable=True)
    timestamp_inserted_utc = Column(TIMESTAMP, default=utcnow_naive, nullable=False)

    def __repr__(self):
        return f"{self.user_rel} - {self.credential_id}"


class OAuth(Base):
    """
    Create an OAuth table for storing OAuth tokens
    for modern authentication and authorization.
    NOTE: The OAuthConsumerMixin from Flask-Dance will automatically
    add the necessary fields to store OAuth information.
    """

    __tablename__ = "flask_dance_oauth"
    __table_args__ = {"schema": "public"}
    __abstract__ = True

    id = Column(INTEGER, primary_key=True)
    provider = Column(String(50), nullable=False)
    provider_user_id = Column(String(256), unique=True, nullable=False)
    created_at = Column(TIMESTAMP, default=utcnow_naive, nullable=False)
    token = Column(JSON, nullable=False)

    # user_id = Column(INTEGER, ForeignKey(User.id), nullable=False)
    user_id = Column(INTEGER, ForeignKey("public.users.id"))

    @declared_attr
    def user_rel(self):
        return relationship(
            "User",
            primaryjoin="OAuth.user_id == User.id",
            # secondary=user_role_rel,
            back_populates="oath_rel",
        )


class Role(Base):
    """
    Create a Role table for customer-only or customer-group,
    or all (IJACK users)
    """

    __tablename__ = "roles"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    name = Column(String(60), unique=True, nullable=False)
    description = Column(String(200))

    users_rel = relationship(
        "User", secondary=user_role_rel, back_populates="roles_rel"
    )

    def __repr__(self):
        # return '<Role: {}>'.format(self.name)
        return f"{self.name}"


class VwUserRole(Base):
    """
    View, not table, for seeing users' roles in a nicely formatted report
    """

    __tablename__ = "vw_users_roles"
    __table_args__ = {
        "schema": "public",
        "info": {
            "view": True,
            "script": r"""
                        SELECT id,
                            id AS user_id,
                            name,
                            email,
                            customer,
                            job_title,
                            roles,
                            string_agg(unit, ', '::text) AS units
                        FROM ( SELECT a2.customer,
                                    a2.id,
                                    a2.name,
                                    a2.email,
                                    a2.job_title,
                                    a2.roles,
                                        CASE
                                            WHEN t7.power_unit_str IS NULL AND t6.surface IS NULL THEN ''::text
                                            ELSE concat(t7.power_unit_str, ' (', t6.surface, ')')
                                        END AS unit
                                FROM ( SELECT a1.customer,
                                            a1.id,
                                            a1.name,
                                            a1.email,
                                            a1.job_title,
                                            string_agg(a1.role_name::text, ', '::text) AS roles
                                        FROM ( SELECT t2.id,
                                                    t3.customer,
                                                    concat(t2.first_name, ' ', t2.last_name) AS name,
                                                    t2.email,
                                                    t2.job_title,
                                                    t4.name AS role_name
                                                FROM public.users t2
                                                    LEFT JOIN public.user_role_rel t1 ON t1.user_id = t2.id
                                                    LEFT JOIN public.customers t3 ON t3.id = t2.customer_id
                                                    LEFT JOIN public.roles t4 ON t4.id = t1.role_id
                                                ORDER BY t3.customer, t2.id, t1.role_id) a1
                                        GROUP BY a1.customer, a1.name, a1.email, a1.job_title, a1.id
                                        ORDER BY a1.customer, a1.name) a2
                                    LEFT JOIN public.user_structure_remote_control_rel t5 ON t5.user_id = a2.id
                                    LEFT JOIN structures t6 ON t6.id = t5.structure_id
                                    LEFT JOIN power_units t7 ON t7.id = t6.power_unit_id) a3
                        GROUP BY customer, id, name, email, job_title, roles
                        ORDER BY customer, id, name, email, job_title, roles
                          """,
        },
    }

    id = Column(INTEGER, primary_key=True)
    # NOTE: user_id is the same as "id" above
    user_id = Column(INTEGER)
    customer = Column(VARCHAR)
    name = Column(VARCHAR)
    job_title = Column(VARCHAR)
    email = Column(VARCHAR)
    roles = Column(VARCHAR)
    units = Column(VARCHAR)

    def __repr__(self):
        return f"{self.name} - {self.roles}"


class Customer(Base):
    """Create a Customer table"""

    __tablename__ = "customers"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    customer = Column(String(60), unique=True)
    formal = Column(VARCHAR, unique=True)
    description = Column(String(200))
    is_tax_exempt = Column(BOOLEAN, default=False, nullable=False)
    gst_hst_number = Column(VARCHAR(15), nullable=True)
    mqtt_topic = Column(String(10), nullable=True)

    main_users_rel = relationship(
        "User", back_populates="main_customer_rel", foreign_keys="[User.customer_id]"
    )
    # Many-to-one relationships
    # many users to one customer, and many customers to one user
    users_rel = relationship(
        "User", secondary=user_customer_rel, back_populates="customers_rel"
    )
    cust_sub_groups_rel = relationship("CustSubGroup", back_populates="customers_rel")

    # Many-to-many relationships
    structures_rel = relationship(
        "Structure", secondary=structure_customer_rel, back_populates="customers_rel"
    )
    gateways_rel = relationship(
        "Gw",
        secondary=vw_gw_customer_rel,
        back_populates="customers_rel",
        uselist=True,
        viewonly=True,
        sync_backref=False,
    )
    power_units_rel = relationship(
        "PowerUnit",
        secondary=vw_gw_customer_rel,
        back_populates="customers_rel",
        uselist=True,
        viewonly=True,
        sync_backref=False,
    )

    sim_cards_rel = relationship("SIMCard", back_populates="customers_rel")
    alerts_custom_rel = relationship("AlertCustom", back_populates="customers_rel")
    alerts_sent_maint_rel = relationship(
        "AlertsSentMaint", back_populates="customers_rel"
    )

    # Address fields
    unit = Column(VARCHAR)
    street = Column(VARCHAR)
    city = Column(VARCHAR)
    postal = Column(VARCHAR)

    country_id = Column(INTEGER, ForeignKey("public.countries.id"), nullable=False)
    country_rel = relationship("Country", back_populates="customers_rel")

    province_id = Column(INTEGER, ForeignKey("public.provinces.id"))
    province_rel = relationship("Province", back_populates="customers_rel")

    # NOTE: the following foreign key is circular with the Users.customer_id
    # so before we can drop the table, we first need to run connection.execute(text("SET CONSTRAINTS ALL DEFERRED"))
    accounting_contact_id = Column(
        INTEGER,
        ForeignKey(
            "public.users.id",
            use_alter=True,
            name="customers_accounting_contact_id_fkey",
            deferrable=True,
            initially="DEFERRED",
        ),
    )
    accounting_contact_rel = relationship(
        "User",
        back_populates="accounting_contact_customers_rel",
        foreign_keys=[accounting_contact_id],
    )

    work_orders_rel = relationship(
        "WorkOrder",
        back_populates="customers_rel",
        foreign_keys="[WorkOrder.customer_id]",
    )
    work_orders_creator_company_rel = relationship(
        "WorkOrder",
        back_populates="creator_company_rel",
        foreign_keys="[WorkOrder.creator_company_id]",
    )
    applications_rel = relationship("Application", back_populates="customer_rel")

    def __repr__(self):
        # return '<Customer: {}>'.format(self.customer)
        return str(self.customer)

    def is_canadian(self) -> bool:
        """Is the customer Canadian?"""
        return self.country_id == COUNTRY_ID_CANADA

    @property
    def address(self) -> str:
        """Return the customer's address"""
        addr: str = ""
        if self.unit:
            addr += f"{self.unit} "
        if self.street:
            addr += f"{self.street}\n"
        if self.city:
            addr += f"{self.city}, "
        if self.province_rel:
            addr += f"{self.province_rel}, "
        if self.country_rel:
            addr += f"{self.country_rel}\n"
        if self.postal:
            addr += f"{self.postal}"
        return addr

    # def __unicode__(self):
    #     # attrs = db.class_mapper(self.__class__).column_attrs
    #     attrs = db.class_mapper(self.__class__).attrs  # show also relationships
    #     if "name" in attrs:
    #         return self.name
    #     elif "code" in attrs:
    #         return self.code
    #     else:
    #         return "<%s(%s)>" % (
    #             self.__class__.__name__,
    #             ", ".join(
    #                 "%s=%r" % (k.key, getattr(self, k.key)) for k in sorted(attrs)
    #             ),
    #         )

    # def __str__(self):
    #     return str(self.customer)


class CustSubGroup(Base):
    """
    Create a Customer sub-group table for the customer admin to manage
    """

    __tablename__ = "cust_sub_groups"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    name = Column(String(60), unique=True, nullable=False)
    abbrev = Column(String(30), nullable=False)
    description = Column(String(200))

    # To which customer does this customer sub-group belong?
    customer_id = Column(INTEGER, ForeignKey("public.customers.id"), nullable=False)
    customers_rel = relationship("Customer", back_populates="cust_sub_groups_rel")

    # Many-to-one relationships
    power_units_rel = association_proxy("structures_rel", "power_units_rel")

    # Many-to-many relationships
    # users_rel = relationship(
    #     "User", secondary=user_cust_sub_group_rel, back_populates="cust_sub_groups_rel"
    # )
    # Users who are subscribed to service requests for this customer sub-group
    users_notify_service_requests_rel = relationship(
        "User",
        secondary=user_cust_sub_group_notify_service_requests_rel,
        back_populates="cust_sub_groups_notify_service_requests_rel",
    )
    gateways_rel = relationship(
        "Gw",
        secondary=vw_gw_cust_sub_group_rel,
        back_populates="cust_sub_groups_rel",
        uselist=True,
        viewonly=True,
        sync_backref=False,
    )
    structures_rel = relationship(
        "Structure",
        secondary=structure_cust_sub_group_rel,
        back_populates="cust_sub_groups_rel",
    )

    def __repr__(self) -> str:
        return f"{self.name} ({self.customers_rel})"


class TimeZone(Base):
    """
    Create a time_zones table to select from
    """

    __tablename__ = "time_zones"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    time_zone = Column(VARCHAR, unique=True, nullable=False)
    description = Column(VARCHAR)
    can_show_to_customers = Column(BOOLEAN, default=True, nullable=False)

    country_id = Column(INTEGER, ForeignKey("public.countries.id"), nullable=False)
    country_rel = relationship("Country", back_populates="time_zones_rel")

    # Many-to-one relationships (e.g. many users, who each have one timezone)
    users_rel = relationship("User", back_populates="time_zones_rel")
    structures_rel = relationship("Structure", back_populates="time_zones_rel")
    alerts_custom_rel = relationship("AlertCustom", back_populates="time_zones_rel")
    warehouses_rel = relationship("Warehouse", back_populates="time_zone_rel")

    # service_clock_rel = relationship("ServiceClock", back_populates="time_zone_rel")
    service_clock_in_rel = relationship(
        "ServiceClock",
        back_populates="time_zone_in_rel",
        foreign_keys="[ServiceClock.time_zone_in_id]",
    )
    service_clock_out_rel = relationship(
        "ServiceClock",
        back_populates="time_zone_out_rel",
        foreign_keys="[ServiceClock.time_zone_out_id]",
    )

    def to_pytz(self) -> pytz.timezone:
        """Convert a time zone string to a pytz timezone"""
        return pytz.timezone(self.time_zone)

    def __repr__(self):
        return str(self.time_zone)


class Country(Base):
    """
    Create a countries table to select from
    """

    __tablename__ = "countries"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    country_code = Column(String(2), nullable=False, unique=True)
    country_name = Column(String(45), nullable=False, unique=True)

    # Sales tax rate (e.g. GST in Canada)
    sales_tax_rate = Column(NUMERIC, nullable=False, default=0.0)

    # Many-to-one relationships (e.g. many users, who each have one country)
    users_rel = relationship("User", back_populates="countries_rel")
    customers_rel = relationship("Customer", back_populates="country_rel")
    currency_rel = relationship("Currency", back_populates="country_rel")
    provinces_rel = relationship("Province", back_populates="country_rel")
    time_zones_rel = relationship("TimeZone", back_populates="country_rel")
    applications_rel = relationship("Application", back_populates="countries_rel")
    warehouses_rel = relationship("Warehouse", back_populates="country_rel")
    work_orders_rel = relationship("WorkOrder", back_populates="country_rel")
    # Note: structures_rel removed because Structure relates to Country indirectly through Province

    @classmethod
    def get_country_id_choices(cls):
        """Get country IDs, sorted"""
        not_these_countries = (COUNTRY_ID_CANADA, COUNTRY_ID_USA)
        # pylint: disable=maybe-no-member
        filter_ = ~cls.id.in_(not_these_countries)
        countries = [
            (x.id, x.country_name)
            for x in cls.query.filter(filter_).order_by("country_name")
        ]
        top_countries = [
            (COUNTRY_ID_CANADA, "Canada"),
            (COUNTRY_ID_USA, "United States"),
        ]
        for country in top_countries:
            countries.insert(0, country)
        return countries

    def __repr__(self):
        return str(self.country_name)


class Province(Base):
    """Province or State"""

    __tablename__ = "provinces"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    name = Column(VARCHAR, nullable=False, unique=True)
    abbrev = Column(String(2), nullable=False, unique=True)

    country_id = Column(INTEGER, ForeignKey("public.countries.id"), nullable=False)
    country_rel = relationship("Country", back_populates="provinces_rel")
    zip_code_sales_taxes_rel = relationship(
        "ZipCodeSalesTax", back_populates="province_rel"
    )
    sales_taxes_rel = relationship("SalesTax", back_populates="province_rel")
    work_orders_rel = relationship("WorkOrder", back_populates="province_rel")
    customers_rel = relationship("Customer", back_populates="province_rel")
    counties_rel = relationship("County", back_populates="province_rel")
    applications_rel = relationship("Application", back_populates="province_rel")
    warehouses_rel = relationship("Warehouse", back_populates="province_rel")
    city_rel = relationship("City", back_populates="province_rel")
    structures_rel = relationship("Structure", back_populates="province_rel")

    def __repr__(self):
        return f"{self.name}"


class GeocodingCache(Base):
    """Cache table for reverse geocoding results"""

    __tablename__ = "geocoding_cache"
    __table_args__ = ({"schema": "public"},)

    id = Column(INTEGER, primary_key=True)
    lat_rounded = Column(NUMERIC(8, 5), nullable=False)
    lon_rounded = Column(NUMERIC(8, 5), nullable=False)
    country_id = Column(INTEGER, ForeignKey("public.countries.id"))
    province_id = Column(INTEGER, ForeignKey("public.provinces.id"))
    locality = Column(VARCHAR(100))
    confidence_score = Column(REAL, default=1.0)
    data_source = Column(VARCHAR(20), default="nominatim")
    created_at = Column(TIMESTAMP, default=func.now())
    updated_at = Column(TIMESTAMP, default=func.now())

    # Relationships
    country_rel = relationship("Country")
    province_rel = relationship("Province")

    # Unique constraint on coordinates
    __table_args__ = (
        UniqueConstraint("lat_rounded", "lon_rounded"),
        {"schema": "public"},
    )


class County(Base):
    """Counties within states (for US states)"""

    __tablename__ = "counties"
    # Make table unique on name/province_id combination
    __table_args__ = (
        UniqueConstraint("name", "province_id"),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)
    name = Column(VARCHAR, nullable=False)
    description = Column(TEXT)
    county_code = Column(VARCHAR)

    province_id = Column(INTEGER, ForeignKey("public.provinces.id"), nullable=False)
    province_rel = relationship("Province", back_populates="counties_rel")

    work_orders_rel = relationship("WorkOrder", back_populates="county_rel")

    zip_code_sales_taxes_rel = relationship(
        "ZipCodeSalesTax", back_populates="county_rel"
    )
    city_rel = relationship("City", back_populates="county_rel")

    def __repr__(self):
        if self.province_rel:
            return f"{self.name} ({self.province_rel.name})"
        return f"{self.name}"


class City(Base):
    """Model for cities"""

    __tablename__ = "cities"
    __table_args__ = (
        Index("idx_city_province_county", "province_id", "county_id"),
        # Create a unique constraint on city name within a county
        UniqueConstraint("county_id", "name", name="cities_county_id_name_key"),
        {"schema": "public"},
    )

    # Primary key for the city
    id = Column(INTEGER, primary_key=True)
    # City name
    name = Column(VARCHAR, nullable=False)
    # # Additional city-level tax rate
    # tax_rate = Column(db.Numeric(5, 4), nullable=False, default=0.0)

    # Foreign key references
    province_id = Column(INTEGER, ForeignKey("public.provinces.id"), nullable=False)
    province_rel = relationship("Province", back_populates="city_rel")

    # Canadian cities don't have counties, so this maybe should be nullable in the future
    county_id = Column(INTEGER, ForeignKey("public.counties.id"), nullable=False)
    county_rel = relationship("County", back_populates="city_rel")

    # Relationships
    zip_code_sales_taxes_rel = relationship(
        "ZipCodeSalesTax", back_populates="city_rel"
    )
    zip_codes_rel = relationship("ZipCode", back_populates="city_rel")
    work_orders_rel = relationship("WorkOrder", back_populates="city_rel")

    def __repr__(self):
        return f"{self.name} ({self.province_rel})"


class ZipCode(Base):
    """Model for zip codes"""

    __tablename__ = "zip_codes"
    __table_args__ = (
        # Create a unique constraint on zip code within a city
        UniqueConstraint("city_id", "zip_code", name="uq_zip_code_city"),
        {"schema": "public"},
    )

    # Primary key for the zip code
    id = Column(INTEGER, primary_key=True)
    # Zip code
    zip_code = Column(VARCHAR(10), nullable=False)

    # Foreign key references
    city_id = Column(INTEGER, ForeignKey("public.cities.id"), nullable=False)
    city_rel = relationship("City", back_populates="zip_codes_rel")

    # Relationships
    zip_code_sales_taxes_rel = relationship(
        "ZipCodeSalesTax", back_populates="zip_code_rel"
    )
    work_orders_rel = relationship("WorkOrder", back_populates="zip_code_rel")

    def __repr__(self):
        return f"{self.zip_code} ({self.city_rel})"


class ZipCodeSalesTax(Base):
    """
    Sales tax by zip code, from this site:
    https://www.avalara.com/taxrates/en/download-tax-tables.html
    Inserted into database with ad hoc Docker program 'insert_zip_code_sales_taxes_into_db_from_csv.py'
    """

    __tablename__ = "zip_code_sales_tax"
    __table_args__ = (
        # Unique constraint on zip code, state, county, and city
        UniqueConstraint(
            "zip_code_id",
            "state_id",
            "county_id",
            "city_id",
            name="zip_code_sales_tax_state_id_zip_code_id_county_id_city_id_key",
        ),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)
    timestamp_utc_updated = Column(TIMESTAMP, nullable=False, default=utcnow_naive)

    zip_code_id = Column(INTEGER, ForeignKey("public.zip_codes.id"), nullable=False)
    zip_code_rel = relationship("ZipCode", back_populates="zip_code_sales_taxes_rel")

    state_id = Column(INTEGER, ForeignKey("public.provinces.id"), nullable=False)
    province_rel = relationship("Province", back_populates="zip_code_sales_taxes_rel")

    county_id = Column(INTEGER, ForeignKey("public.counties.id"), nullable=False)
    county_rel = relationship("County", back_populates="zip_code_sales_taxes_rel")

    city_id = Column(INTEGER, ForeignKey("public.cities.id"), nullable=False)
    city_rel = relationship("City", back_populates="zip_code_sales_taxes_rel")

    tax_region_name = Column(VARCHAR)
    state_rate = Column(NUMERIC)
    county_rate = Column(NUMERIC)
    city_rate = Column(NUMERIC)
    special_rate = Column(NUMERIC)
    risk_level = Column(SMALLINT)

    # Calculated field summing state and county rates
    state_plus_county = Column(
        NUMERIC,
        Computed("state_rate + county_rate"),
    )
    state_county_other = Column(
        NUMERIC,
        Computed("state_rate + county_rate + special_rate"),
    )
    # Calculated field summing all rates
    combined_rate_est = Column(
        NUMERIC, Computed("state_rate + county_rate + city_rate + special_rate")
    )

    def __repr__(self):
        return f"{self.zip_code_rel} - {self.combined_rate_est}"


class SalesTax(Base):
    """Sales tax rates by state"""

    __tablename__ = "sales_taxes"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)

    province_id = Column(
        INTEGER,
        ForeignKey("public.provinces.id"),
        default=PROVINCE_ID_SK,
        nullable=False,
    )
    province_rel = relationship("Province", back_populates="sales_taxes_rel")

    rate = Column(NUMERIC, nullable=False)

    # We're just recording the province, and hard-coding the rate, since it changes over time
    # work_orders_rel = relationship("WorkOrder", back_populates="sales_tax_rel")

    def __repr__(self):
        return f"{self.province_rel} ({self.rate}%)"


class Currency(Base):
    """Currency for invoicing"""

    __tablename__ = "currencies"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    name = Column(VARCHAR, nullable=False, unique=True)
    description = Column(TEXT, nullable=True)
    fx_rate_cad_per = Column(NUMERIC, nullable=False, default=1.0)

    country_id = Column(INTEGER, ForeignKey("public.countries.id"), nullable=False)

    @declared_attr
    def country_rel(cls):
        return relationship("Country", back_populates="currency_rel")

    @declared_attr
    def work_orders_rel(cls):
        return relationship("WorkOrder", back_populates="currency_rel")

    def __repr__(self):
        return f"{self.name}"


class CurrencyRate(Base):
    """Historical currency exchange rates"""

    __tablename__ = "currencies_rates"
    __table_args__ = (
        UniqueConstraint("currency_id", "rate_date", name="uq_currency_rate_date"),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)
    currency_id = Column(INTEGER, ForeignKey("public.currencies.id"), nullable=False)
    rate_date = Column(DATE, nullable=False)
    fx_rate_cad_per = Column(NUMERIC(10, 6), nullable=False)
    source = Column(VARCHAR(50), nullable=False, default="exchangerate-api.com")

    @declared_attr
    def currency_rel(cls):
        return relationship("Currency", backref="historical_rates")

    def __repr__(self):
        return f"{self.currency_rel.name if self.currency_rel else 'Unknown'} - {self.rate_date}: {self.fx_rate_cad_per} CAD"


class Gateway(Base):
    """
    Create a public.gateway "VIEW" representation, not to be confused with the public.gw table
    """

    __tablename__ = "gateways"  # This is actually a view, not a table
    __table_args__ = {
        "schema": "public",
        "info": {
            "view": True,
            "script": r"""
SELECT DISTINCT ON (gw.id, cust.customer) gw.id AS gateway_id,
    gw.gateway,
    gw.aws_thing,
    gw.mac,
    pu.id AS power_unit_id,
    pu.power_unit,
    pu.power_unit_str,
    str.id AS structure_id,
    str.structure,
    str.structure_slave_id,
    str_sl.structure AS structure_slave,
    gw.ready_and_working,
    pu.apn,
    pu.alerts_edge,
    pu.change_detect_sens,
    tz.id AS time_zone_id,
    tz.time_zone,
    pu.wait_time_mins,
    pu.wait_time_mins_ol,
    pu.wait_time_mins_suction,
    pu.wait_time_mins_discharge,
    pu.wait_time_mins_spm,
    pu.wait_time_mins_stboxf,
    pu.wait_time_mins_hyd_temp,
    pu.hyd_oil_lvl_thresh,
    pu.hyd_filt_life_thresh,
    pu.hyd_oil_life_thresh,
    pu.wait_time_mins_hyd_oil_lvl,
    pu.wait_time_mins_hyd_filt_life,
    pu.wait_time_mins_hyd_oil_life,
    pu.wait_time_mins_chk_mtr_ovld,
    pu.wait_time_mins_pwr_fail,
    pu.wait_time_mins_soft_start_err,
    pu.wait_time_mins_grey_wire_err,
    pu.wait_time_mins_ae011,
    pu.heartbeat_enabled,
    pu.online_hb_enabled,
    pu.suction,
    pu.discharge,
    pu.spm,
    pu.stboxf,
    pu.hyd_temp,
    str.gps_lat,
    str.gps_lon,
    str.downhole,
    str.surface,
    str.location,
    str.well_license,
    cust.id AS customer_id,
    cust.customer,
    cust_sub.id AS cust_sub_group_id,
    cust_sub.name AS cust_sub_group,
    cust_sub.abbrev AS cust_sub_group_abbrev,
    str.model_type_id,
    mt.model,
    mt.unit_type_id,
    ut.unit_type,
    mt.unit_type_id AS model_unit_type_id,
    ut.unit_type AS model_unit_type,
    str.model_type_id_slave,
    mt_sl.model AS model_slave,
    mt_sl.unit_type_id AS model_unit_type_id_slave,
    ut_sl.unit_type AS model_unit_type_slave,
    sim.sim_card,
    (string_to_array(cust.mqtt_topic::text, ' '::text))[1] AS mqtt_topic,
    str.status,
        CASE
            WHEN alerts.power_unit_id IS NULL THEN false
            ELSE true
        END AS alerts
   FROM gw gw
     LEFT JOIN power_units pu ON gw.power_unit_id = pu.id
     LEFT JOIN structures str ON pu.id = str.power_unit_id
     LEFT JOIN structures str_sl ON str.structure_slave_id = str_sl.id
     LEFT JOIN public.structure_customer_rel str_cust_rel ON str_cust_rel.structure_id = str.id
     LEFT JOIN public.customers cust ON str_cust_rel.customer_id = cust.id
     LEFT JOIN ( SELECT DISTINCT alerts_1.power_unit_id
           FROM alerts alerts_1) alerts ON pu.id = alerts.power_unit_id
     LEFT JOIN public.time_zones tz ON str.time_zone_id = tz.id
     LEFT JOIN sim_cards sim ON gw.id = sim.gateway_id
     LEFT JOIN public.model_types mt ON str.model_type_id = mt.id
     LEFT JOIN public.unit_types ut ON str.unit_type_id = ut.id
     LEFT JOIN public.model_types mt_sl ON str_sl.model_type_id_slave = mt_sl.id
     LEFT JOIN public.unit_types ut_sl ON str_sl.unit_type_id = ut_sl.id
     LEFT JOIN public.structure_cust_sub_group_rel csr ON csr.structure_id = str.id
     LEFT JOIN public.cust_sub_groups cust_sub ON cust_sub.id = csr.cust_sub_group_id
  ORDER BY gw.id, cust.customer
            """,
        },
    }

    gateway_id = Column(INTEGER, primary_key=True)
    gateway = Column(VARCHAR)
    aws_thing = Column(VARCHAR)
    mac = Column(VARCHAR)
    # power_unit_id = Column(INTEGER, primary_key=True) # AJAX loading only supports one primary key
    power_unit_id = Column(INTEGER)
    power_unit = Column(DOUBLE_PRECISION)
    # structure_id = Column(INTEGER, primary_key=True) # AJAX loading only supports one primary key
    structure_id = Column(INTEGER)
    structure = Column(DOUBLE_PRECISION)
    ready_and_working = Column(BOOLEAN)
    apn = Column(VARCHAR)
    change_detect_sens = Column(NUMERIC)
    time_zone = Column(VARCHAR)
    time_zone_id = Column(INTEGER)
    wait_time_mins = Column(SMALLINT)
    wait_time_mins_ol = Column(SMALLINT)
    wait_time_mins_suction = Column(SMALLINT)
    wait_time_mins_discharge = Column(SMALLINT, nullable=False, default=240)
    wait_time_mins_spm = Column(SMALLINT, nullable=False, default=60)
    wait_time_mins_stboxf = Column(SMALLINT)
    wait_time_mins_hyd_temp = Column(INTEGER)
    heartbeat_enabled = Column(BOOLEAN)
    online_hb_enabled = Column(BOOLEAN)
    suction = Column(INTEGER)
    discharge = Column(INTEGER)
    spm = Column(INTEGER)
    stboxf = Column(SMALLINT)
    hyd_temp = Column(INTEGER)
    gps_lat = Column(DOUBLE_PRECISION)
    gps_lon = Column(DOUBLE_PRECISION)
    downhole = Column(VARCHAR)
    surface = Column(VARCHAR)
    location = Column(VARCHAR)
    well_license = Column(VARCHAR)
    # customer_id = Column(INTEGER, primary_key=True) # AJAX loading only supports one primary key
    customer_id = Column(INTEGER)
    customer = Column(VARCHAR)
    cust_sub_group_id = Column(INTEGER)
    cust_sub_group = Column(VARCHAR)
    cust_sub_group_abbrev = Column(VARCHAR)
    unit_type_id = Column(INTEGER)
    unit_type = Column(VARCHAR)
    model_type_id = Column(INTEGER)
    model = Column(VARCHAR)
    model_unit_type_id = Column(INTEGER)
    model_unit_type = Column(VARCHAR)
    model_type_id_slave = Column(INTEGER)
    model_slave = Column(VARCHAR)
    model_unit_type_id_slave = Column(INTEGER)
    model_unit_type_slave = Column(VARCHAR)
    sim_card = Column(VARCHAR)
    mqtt_topic = Column(VARCHAR)
    status = Column(VARCHAR)
    # alerts = Column(BOOLEAN)
    alerts_edge = Column(BOOLEAN, nullable=False, default=True)

    def __repr__(self):
        pu = remove_unnecessary_decimal(
            view=None, context=None, model=self, name="power_unit"
        )
        try:
            if self.downhole[0] in ("", None):
                return f"{pu} - {get_string_rep(self.surface)}"
            else:
                return f"{pu} - {get_string_rep(self.downhole)} @ {get_string_rep(self.surface)}"
        except Exception:
            return f"{pu} - {get_string_rep(self.downhole)} @ {get_string_rep(self.surface)}"


class Gw(Base):
    """
    Create a public.gw table representation, not to be confused with the public.gateways view,
    which is a view of public.gw and others such as public.structures, etc.
    """

    __tablename__ = "gw"
    __table_args__ = {"schema": "public"}

    # __mapper_args__ = {
    #     "polymorphic_identity": "gw",
    #     "with_polymorphic": "*",
    #     # "enable_typechecks": False,
    #     # "polymorphic_identity": 0,
    #     "polymorphic_on": id,
    #     # "polymorphic_on": Gw,
    #     # "concrete": True,
    # }

    # This is the MQTT topic gateway, for querying time series data
    id = Column(INTEGER, primary_key=True)
    # Note the utcnow_naive is passed as a function, not the value of the function
    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    # gateway = Column(TEXT, unique=True, nullable=False)
    gateway = Column(TEXT, unique=True, nullable=False)
    aws_thing = Column(TEXT, unique=True, nullable=False)
    mac = Column(TEXT)
    # TODO: Sometimes gateways get new MAC IDs and they still have the same serial number...
    # Not sure what's the optimal thing to do here...
    # serial_gw = Column(VARCHAR, unique=True)
    serial_gw = Column(TEXT)
    model_gw = Column(TEXT, unique=False)
    imei = Column(VARCHAR, unique=True)
    notes = Column(TEXT)
    # apn = Column(VARCHAR)
    # well_license = Column(VARCHAR)

    gateway_type_id = Column(
        INTEGER,
        ForeignKey("public.gateway_types.id"),
        default=GATEWAY_TYPE_ID_AXIOMTEK,
    )
    gateway_types_rel = relationship("GatewayType", back_populates="gateways_rel")

    # One-to-one relationship (in the PowerUnit class, uselist=False)
    power_unit_id = Column(INTEGER, ForeignKey("public.power_units.id"))
    power_units_rel = relationship("PowerUnit", back_populates="gateways_rel")
    # We need this for the __str__ below
    power_unit_str = association_proxy("power_units_rel", "power_unit_str")

    # The following relationship might be temporary, but it's a way to directly relate a gateway to a structure
    structure_id = Column(INTEGER, ForeignKey("public.structures.id"))
    # structures_rel = relationship(
    #     "Structure",
    #     foreign_keys="[Gw.structure_id]",
    #     back_populates="gateways_rel",
    #     sync_backref=False,
    # )

    # We need this structure_id to access the surface and downhole locations on the structures table
    # Otherwise it's super-complicated trying to get surface and downhole via the common
    # relationship with the public.power_units table... This is MUCH MUCH easier!
    # structure_id = Column(INTEGER, ForeignKey('public.structures.id'))
    structures_rel = relationship(
        "Structure",
        secondary=vw_gw_structure_rel,
        back_populates="gateways_rel",
        uselist=True,
        viewonly=True,
        sync_backref=False,
    )
    # We need this for the __str__ below
    downhole = association_proxy("structures_rel", "downhole")
    surface = association_proxy("structures_rel", "surface")

    cust_sub_groups_rel = relationship(
        "CustSubGroup",
        secondary=vw_gw_cust_sub_group_rel,
        back_populates="gateways_rel",
        uselist=True,
        viewonly=True,
        sync_backref=False,
    )

    # Both of the below relationships work for display, but the relationship is more powerful since it's iterable
    # customer = association_proxy('structure', 'customer')
    # customers_rel = relationship('Customer',
    #     secondary='public.structures',
    #     primaryjoin='Gw.structure_id == Structure.id',
    #     secondaryjoin='Structure.customer_id == Customer.id',
    #     uselist=False, # only one customer per structure
    #     viewonly=True,
    #     # back_populates='units'
    # )
    # customers = customer
    # customers_rel = relationship('Customer',
    #     secondary='public.structure_customer_rel',
    #     primaryjoin='Gw.structure_id == Structure.id',
    #     secondaryjoin='public.structure_customer_rel.customer_id == public.customers.id',
    #     uselist=True, # many customers per structure
    #     viewonly=True,
    # )
    customers_rel = relationship(
        "Customer",
        secondary=vw_gw_customer_rel,
        back_populates="gateways_rel",
        uselist=True,
        viewonly=True,
        sync_backref=False,
    )
    # We need customer_id so the customers can see only their units
    customer_id = association_proxy("customers_rel", "id")

    # Are the gateways fully tested and ready to go?
    test_cellular = Column(BOOLEAN, default=False, nullable=False)
    test_can_bus = Column(BOOLEAN, default=False, nullable=False)
    # We only manually change the following to false if a test has failed and something's wrong
    ready_and_working = Column(BOOLEAN, default=True, nullable=False)
    location_gw = Column(TEXT)

    test_cellular_user_rel = relationship(
        "GwTestedCellular",
        cascade="all, delete-orphan",
        passive_deletes=True,
        back_populates="gateways_rel",
        # backref='version',
        # foreign_keys="Application.gateway_id",
        # secondary=gw_tested_cellular,
        # backref=db.backref("gateways", lazy="dynamic")
        # uselist=True,
        # viewonly=True,
        # sync_backref=False,
    )

    not_connected_dont_worry_rel = relationship(
        "GwNotConnectedDontWorry",
        cascade="all, delete-orphan",
        passive_deletes=True,
        back_populates="gateways_rel",
    )

    # One-to-one relationship. "uselist=False" forces a one-to-one relationship
    sim_card_rel = relationship("SIMCard", back_populates="gateways_rel", uselist=False)

    # one-to-one relationship
    gateway_info_rel = relationship(
        "GwInfo", back_populates="gateways_rel", uselist=False
    )
    # gateway_info_time_since_reported = association_proxy(
    #     "gateway_info_rel", "time_since_reported"
    # )
    gateway_info_days_since_reported = association_proxy(
        "gateway_info_rel", "days_since_reported"
    )
    mqtt_messages_rel = relationship("MQTTMessage", back_populates="gateway_rel")

    def get_gw_pu_loc_str(self):
        """
        Get a string like 'power_unit - downhole @ surface - gateway'
        NOTE: This function is very slow! Use the get_gateway_options_list() utility function instead!
        """
        return f"{self.gateway} - {self}"

    def __repr__(self):
        """Unambiguous"""
        return str(self.gateway)

    def __str__(self):
        """
        Readable and understandable by customer.
        Note each gateway might have more than one structure/location.
        NOTE: This function is very slow! Use the get_gateway_options_list() utility function instead!
        """
        # This function is very slow...
        # pu = remove_unnecessary_decimal(
        #     view=None, context=None, model=self, name="power_unit"
        # )
        # if isinstance(self.power_unit_str, str):
        #     pu = self.power_unit_str.replace(".0", "")
        # else:

        pu = self.power_unit_str if self.power_unit_str else "No power unit"

        try:
            downhole = self.downhole[0]
        except IndexError:
            downhole = None
        else:
            downhole = None if downhole == "" else downhole

        try:
            surface = self.surface[0]
        except IndexError:
            surface = None
        else:
            surface = None if surface == "" else surface

        if surface is None:
            surface = "No land location" if downhole is None else "No surface location"

        # surface = get_string_rep(surface)

        if downhole is None:
            return f"{pu} - {surface}"
        else:
            # return f"{pu} - {get_string_rep(downhole)} @ {surface}"
            return f"{pu} - {downhole} @ {surface}"


# class GwStrGateway(Gw):
#     """Same as class Gw, just a different repr()"""

#     # Failed to create record. Attempting to flush an item of type <class 'shared.models.models.GwStrGateway'>
#     # as a member of collection "GwNotConnectedDontWorry.gateway". Expected an object of
#     # type <class 'shared.models.models.Gw'> or a polymorphic subclass of this type.
#     # If <class 'shared.models.models.GwStrGateway'> is a subclass of <class 'shared.models.models.Gw'>,
#     # configure mapper "mapped class Gw->gw" to load this subtype polymorphically,
#     # or set enable_typechecks=False to allow any subtype to be accepted for flush.
#     __mapper_args__ = {
#         "polymorphic_identity": "gw",
#         "with_polymorphic": "*",
#         # "enable_typechecks": False,
#         # "polymorphic_identity": 0,
#         # "polymorphic_on": type,
#         # "polymorphic_on": Gw,
#         # "concrete": True,
#     }

#     def __str__(self):
#         return f"{self.aws_thing} (PU {self.power_unit_str})"


class GwTestedCellular(Base):
    """
    Relationship table between gateways and users,
    and the timestamp at which they tested cellular
    """

    __tablename__ = "gw_tested_cellular"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    timestamp_utc = Column(TIMESTAMP, nullable=False, default=utcnow_naive)

    gateway_id = Column(
        INTEGER,
        ForeignKey("public.gw.id", ondelete="CASCADE"),
        nullable=False,
    )
    # gateways_rel = relationship("Gw", backref="gw_tested_cellular")
    gateways_rel = relationship("Gw", back_populates="test_cellular_user_rel")

    user_id = Column(
        INTEGER,
        ForeignKey("public.users.id", ondelete="CASCADE"),
        nullable=False,
    )
    users_rel = relationship("User", backref="gw_tested_cellular")
    users_rel_first_name = association_proxy("users_rel", "first_name")
    users_rel_last_name = association_proxy("users_rel", "last_name")
    users_rel_email = association_proxy("users_rel", "email")

    network_id = Column(
        SMALLINT,
        ForeignKey("public.gw_cell_networks.id", ondelete="CASCADE"),
    )
    network_rel = relationship(
        "GwCellNetworks", back_populates="gw_tested_cellular_rel"
    )

    def __repr__(self):
        """Unambiguous"""
        return f"{self.users_rel_first_name} {self.users_rel_last_name} - {self.timestamp_utc}"

    def __str__(self):
        """
        Readable and understandable by customer.
        """
        return f"{self.users_rel_first_name} {self.users_rel_last_name} ({self.timestamp_utc} - {self.network_rel})"


class GwNotConnectedDontWorry(Base):
    """
    Relationship table between gateways and users,
    and the timestamp at which someone confirmed we
    don't need to worry about this gateway being offline
    """

    __tablename__ = "gw_not_connected_dont_worry"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    timestamp_utc = Column(TIMESTAMP, nullable=False, default=utcnow_naive)

    gateway_id = Column(
        INTEGER,
        ForeignKey("public.gw.id", ondelete="CASCADE"),
        nullable=False,
    )
    gateways_rel = relationship(
        "Gw",
        # secondary=vw_gw_structure_rel,
        back_populates="not_connected_dont_worry_rel",
    )
    # aws_thing = association_proxy("gateways_rel", "aws_thing")
    # power_unit_str = association_proxy("gateways_rel", "power_unit_str")
    # customer = association_proxy("gateways_rel", "customer")
    # gateway_type = association_proxy("gateways_rel", "gateway_type")
    # surface = association_proxy("gateways_rel", "surface")
    # # customers_rel = association_proxy("gateways_rel", "customers_rel")
    # # gateway_info = association_proxy("gateways_rel", "gateway_info")
    # gateway_info_time_since_reported = association_proxy(
    #     "gateways_rel", "gateway_info_time_since_reported"
    # )
    gateway_info_days_since_reported = association_proxy(
        "gateways_rel", "gateway_info_days_since_reported"
    )

    # test_cellular_user = association_proxy("gateway", "test_cellular_user")

    user_id = Column(
        INTEGER,
        ForeignKey("public.users.id", ondelete="CASCADE"),
        nullable=False,
        default=USER_ID_SEAN,
    )
    users_rel = relationship(
        "User", backref="not_connected_dont_worry", info={"default": USER_ID_SEAN}
    )
    # users_rel_first_name = association_proxy("user", "first_name")
    # user_last_name = association_proxy("user", "last_name")
    # user_email = association_proxy("user", "email")

    am_i_worried = Column(BOOLEAN, nullable=False, default=False)
    notes = Column(TEXT)
    operators_contacted = Column(TIMESTAMP)

    def __repr__(self):
        """Unambiguous"""
        return f"{self.users_rel} - {self.timestamp_utc}"

    def __str__(self):
        """
        Readable and understandable by customer.
        """
        return f"{self.users_rel} ({self.timestamp_utc})"


# Gw.test_cellular_user_rel = relationship(
#     "GwTestedCellular",
#     # backref='version',
#     # foreign_keys="Application.gateway_id",
#     # secondary="gw_tested_cellular",
#     # backref=db.backref("gateways", lazy="dynamic")
#     back_populates="gateways_rel",
#     # uselist=True,
#     # viewonly=True,
#     # sync_backref=False,
#     cascade="all, delete-orphan",
#     passive_deletes=True,
# )


class GwCellNetworks(Base):
    """
    Cellular networks like SaskTel, Lucky Mobile, Koodo, etc.
    For testing cellular on the gateways and keeping a record of it.
    """

    __tablename__ = "gw_cell_networks"
    __table_args__ = {"schema": "public"}

    id = Column(SMALLINT, primary_key=True)
    name = Column(VARCHAR, nullable=False)
    description = Column(VARCHAR)

    gw_tested_cellular_rel = relationship(
        "GwTestedCellular", back_populates="network_rel"
    )

    def __str__(self):
        """
        Readable and understandable by customer.
        """
        return f"{self.name}"


class GwInfo(Base):
    """
    Gateway info from the gateway itself, self-reporting.
    Updated by postgresql_scheduler Docker container.
    """

    __tablename__ = "gw_info"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)

    # one-to-one relationship
    gateway_id = Column(
        INTEGER, ForeignKey("public.gw.id"), nullable=False, unique=True
    )
    gateways_rel = relationship("Gw", back_populates="gateway_info_rel")
    gateway_types_rel = association_proxy("gateways_rel", "gateway_types_rel")

    # Define relationship to PowerUnit through the gateway
    # This uses a primaryjoin condition to link through the gateway table
    power_unit_rel = relationship(
        "PowerUnit",
        # Join through the gateway table Since there's no direct foreign key between GwInfo and PowerUnit
        secondary="public.gw",
        primaryjoin="GwInfo.gateway_id == Gw.id",
        secondaryjoin="Gw.power_unit_id == PowerUnit.id",
        back_populates="gateway_info_rel",
        # Makes the relationship read-only to prevent SQLAlchemy from trying to
        # manage inserts/updates through this relationship
        viewonly=True,
        # By default, SQLAlchemy returns a list for relationships
        # uselist=False makes it return a single object instead
        # This means power_unit_rel will be a single PowerUnit object, not a list
        uselist=False,
    )

    timestamp_utc_updated = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    timestamp_utc_last_reported = Column(TIMESTAMP, nullable=False)
    aws_thing = Column(VARCHAR, nullable=False)
    power_unit_str = Column(VARCHAR)
    os_name = Column(VARCHAR)
    os_pretty_name = Column(VARCHAR)
    os_version = Column(VARCHAR)
    os_version_id = Column(VARCHAR)
    os_release = Column(VARCHAR)
    os_machine = Column(VARCHAR)
    os_platform = Column(VARCHAR)
    os_python_version = Column(VARCHAR)
    modem_model = Column(VARCHAR)
    modem_firmware_rev = Column(VARCHAR)
    modem_drivers = Column(VARCHAR)
    sim_operator = Column(VARCHAR)
    swv_canpy = Column(VARCHAR)
    swv_plc = Column(VARCHAR)
    time_since_reported = Column(VARCHAR)
    days_since_reported = Column(REAL)
    connected = Column(BOOLEAN)
    hyd = Column(SMALLINT)
    warn1 = Column(SMALLINT)
    warn2 = Column(SMALLINT)
    hours = Column(REAL)
    suction = Column(REAL)
    discharge = Column(REAL)
    spm = Column(REAL)
    drive_size_gb = Column(REAL)
    drive_used_gb = Column(REAL)
    memory_size_gb = Column(REAL)
    memory_used_gb = Column(REAL)
    suction_range = Column(SMALLINT)
    has_slave = Column(BOOLEAN)


class MQTTMessage(Base):
    """MQTT messages from the gateways"""

    __tablename__ = "mqtt_messages"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    timestamp_utc = Column(TIMESTAMP, nullable=False, default=utcnow_naive)

    topic = Column(VARCHAR, nullable=False)
    message = Column(TEXT, nullable=False)
    is_send = Column(BOOLEAN, nullable=False, default=False)
    rc = Column(SMALLINT, nullable=False, default=0)
    rc_message = Column(
        VARCHAR,
        nullable=False,
        # default="MQTT_ERR_SUCCESS"
    )

    gateway_id = Column(
        INTEGER, ForeignKey("public.gw.id", ondelete="CASCADE"), nullable=False
    )
    gateway_rel = relationship("Gw", back_populates="mqtt_messages_rel")

    def __repr__(self):
        return f"{self.gateway_rel} - {self.timestamp_utc}"


class ReleaseNote(Base):
    """Create a public.release_notes table representation"""

    __tablename__ = "release_notes"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    timestamp_utc = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    version = Column(DOUBLE_PRECISION, nullable=False, unique=True)
    description = Column(TEXT, nullable=False)
    is_stable = Column(BOOLEAN, nullable=False, default=True)


class Structure(Base):
    """Create a public.structures table representation"""

    __abstract__ = True
    __tablename__ = "structures"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    structure = Column(DOUBLE_PRECISION, nullable=False, unique=True)
    # This is a calculated/generated column (new to PostgreSQL 12) so we can search it
    # as a string-based association proxy in form AJAX dropdown menu lookups
    structure_str = Column(VARCHAR(10), Computed("CAST(structure as VARCHAR(10))"))

    structure_slave_id = Column(
        INTEGER, ForeignKey("public.structures.id"), unique=True
    )

    @declared_attr
    def structure_slave_rel(self):
        return relationship("Structure", remote_side=[self.id], lazy="select")

    power_unit_id = Column(INTEGER, ForeignKey("public.power_units.id"))
    # one-to-many

    @declared_attr
    def power_units_rel(self):
        return relationship(
            "PowerUnit",
            foreign_keys=[self.power_unit_id],
            back_populates="structures_rel",
        )

    # Allow this one association_proxy since it's used not just in Flask-Admin
    @declared_attr
    def power_unit_str(self):
        return association_proxy("power_units_rel", "power_unit_str")

    # # power = association_proxy('power_units_rel', 'power')
    # # voltage = association_proxy('power_units_rel', 'voltage')
    # power_unit_type = association_proxy("power_units_rel", "power_unit_type")
    # power_unit_notes = association_proxy("power_units_rel", "notes")

    on_genset = Column(BOOLEAN, nullable=False, default=False)
    auto_greaser = Column(BOOLEAN, nullable=True, default=False)
    auto_greaser_needs_alerts = Column(BOOLEAN, nullable=True, default=False)

    # Many-to-many relationships
    @declared_attr
    def customers_rel(self):
        return relationship(
            "Customer",
            secondary=structure_customer_rel,
            back_populates="structures_rel",
            uselist=True,
        )

    # # For filtering structures by customer_id in Flask-Admin
    # customer_id = association_proxy("customers_rel", "id")

    @declared_attr
    def alerts_custom_rel(self):
        return relationship(
            "AlertCustom",
            secondary=alerts_custom_structure_rel,
            back_populates="structures_rel",
        )

    # Customer sub-group(s) are optional, which is why we still need customer_id above.
    # Many-to-many relationship
    @declared_attr
    def cust_sub_groups_rel(self):
        return relationship(
            "CustSubGroup",
            secondary=structure_cust_sub_group_rel,
            back_populates="structures_rel",
        )

    model_type_id = Column(INTEGER, ForeignKey("public.model_types.id"))
    model_type_id_slave = Column(INTEGER, ForeignKey("public.model_types.id"))
    # one-to-many

    @declared_attr
    def model_types_rel(self):
        return relationship(
            "ModelType",
            foreign_keys=[self.model_type_id],
            back_populates="structures_rel",
        )

    # one-to-many

    # one-to-many
    @declared_attr
    def model_type_slaves_rel(self):
        return relationship("ModelType", foreign_keys="[Structure.model_type_id_slave]")

    # one-to-many
    @declared_attr
    def unit_types_rel(self):
        return relationship(
            "UnitType",
            foreign_keys="[Structure.unit_type_id]",
            back_populates="structures_rel",
        )

    unit_type_id = Column(INTEGER, ForeignKey("public.unit_types.id"))
    # one-to-many

    # One-to-many relationship (e.g. one gateway can serve several structures in an UNOGAS structure)
    # In the future, we should move this relationship through the PowerUnit class instead,
    # since the gateway is in the power unit...
    # gateways_rel = relationship(
    #     "Gw",
    #     secondary=vw_gw_structure_rel,
    #     back_populates="structures_rel",
    #     uselist=True,
    #     viewonly=True,
    #     sync_backref=False,
    # )
    # Joining a many-to-many relationship via an association table
    # https://stackoverflow.com/a/19736109/3385948
    @declared_attr
    def gateways_rel(self):
        return relationship(
            "Gw",
            secondary=vw_gw_structure_rel,
            back_populates="structures_rel",
            uselist=True,
            viewonly=True,
            sync_backref=False,
        )

    @declared_attr
    def aws_thing(self):
        return association_proxy("gateways_rel", "aws_thing")

    # Remote control permissions by structure_id/user_id

    @declared_attr
    def users_rel(self):
        return relationship(
            "User",
            secondary=user_structure_remote_control_rel,
            back_populates="structures_rel",
        )

    @declared_attr
    def operators_users_rel(self):
        return relationship(
            "User",
            secondary=user_structure_operators_rel,
            back_populates="structures_operators_rel",
        )

    @declared_attr
    def maintenance_users_rel(self):
        return relationship(
            "User",
            secondary=user_structure_maintenance_rel,
            back_populates="maintenance_structures_rel",
        )

    @declared_attr
    def sales_users_rel(self):
        return relationship(
            "User",
            secondary=user_structure_sales_rel,
            back_populates="sales_structures_rel",
        )

    well_license = Column(TEXT)
    qb_sale = Column(TEXT)
    qb_install = Column(TEXT)
    afe = Column(TEXT)
    dgas_pumpjack = Column(TEXT)

    run_mfg_date = Column(DATE)
    structure_install_date = Column(DATE)
    slave_install_date = Column(DATE)
    # Settable interval between preventative maintenance activities
    op_months_interval = Column(REAL, default=9, nullable=False)
    # Certain units like DGAS, and certain customers like XTO, won't have SIM cards
    has_rcom = Column(BOOLEAN, default=True, nullable=False)

    status = Column(TEXT)
    area = Column(TEXT)

    downhole = Column(TEXT)
    surface = Column(TEXT)
    # Calculated column
    location = Column(
        VARCHAR,
        Computed(
            "CASE WHEN downhole IS NULL OR downhole = '' THEN surface ELSE (downhole || ' @ ') || surface END"
        ),
    )

    # Removed defaults to IJACK SHOP location since UNOGAS combined units cause problems with auto-updating GPS,
    # whether from the website (RCOM) or from the gateway itself via PostgreSQL_Scheduler
    gps_lat = Column(REAL)
    gps_lon = Column(REAL)

    # Geographic analysis fields - Phase 2 enhancement
    province_id = Column(INTEGER, ForeignKey("public.provinces.id"))
    geocoding_status = Column(VARCHAR(20), default="pending", nullable=False)
    geocoding_updated_at = Column(TIMESTAMP)
    auto_geocoded = Column(BOOLEAN, default=False)

    casing_sensor = Column(BOOLEAN)
    notes_1 = Column(TEXT)
    notes_2 = Column(TEXT)
    tundra_name = Column(TEXT)

    # # Performance benchmarking
    # max_spm = Column(REAL)
    # max_suction = Column(REAL)
    # max_discharge = Column(REAL)
    # max_delta_p = Column(REAL)
    # max_discharge_temp = Column(REAL)

    rod_id = Column(INTEGER, ForeignKey("public.rods.id"))
    # one-to-many

    @declared_attr
    def rod_rel(self):
        return relationship("Rod", back_populates="structures_rel")

    barrel_id = Column(INTEGER, ForeignKey("public.barrels.id"))

    # one-to-many
    @declared_attr
    def barrel_rel(self):
        return relationship("Barrel", back_populates="structures_rel")

    shuttle_valve_id = Column(INTEGER, ForeignKey("public.shuttle_valves.id"))
    # one-to-many

    @declared_attr
    def shuttle_valve_rel(self):
        return relationship("ShuttleValve", back_populates="structures_rel")

    check_valve_id = Column(INTEGER, ForeignKey("public.check_valves.id"))
    # one-to-many

    @declared_attr
    def check_valve_rel(self):
        return relationship("CheckValve", back_populates="structures_rel")

    packing_gland_id = Column(INTEGER, ForeignKey("public.packing_glands.id"))
    # one-to-many

    @declared_attr
    def packing_gland_rel(self):
        return relationship("PackingGland", back_populates="structures_rel")

    hyd_piston_type_id = Column(INTEGER, ForeignKey("public.hyd_piston_types.id"))

    @declared_attr
    def hyd_piston_type_rel(self):
        return relationship("HydPistonType", back_populates="structures_rel")

    time_zone_id = Column(SMALLINT, ForeignKey("public.time_zones.id"))

    @declared_attr
    def time_zones_rel(self):
        return relationship("TimeZone", back_populates="structures_rel")

    # Each structure is assigned to a warehouse for parts inventory
    warehouse_id = Column(INTEGER, ForeignKey("public.warehouses.id"))

    @declared_attr
    def warehouse_rel(self):
        return relationship("Warehouse", back_populates="structures_rel")

    # Geographic relationships - Phase 2 enhancement
    @declared_attr
    def province_rel(self):
        return relationship("Province", back_populates="structures_rel")

    # Access country through province relationship via association_proxy
    @declared_attr
    def country_rel(self):
        """Access country through province relationship"""
        return association_proxy("province_rel", "country_rel")

    @declared_attr
    def work_orders_structures_rel(self):
        return relationship(
            "WorkOrder",
            secondary=work_order_structure_rel,
            back_populates="structures_rel",
        )

    @declared_attr
    def work_order_parts_rel(self):
        return relationship(
            "WorkOrderPart",
            back_populates="structures_rel",
        )

    @declared_attr
    def service_rel(self):
        return relationship("Service", back_populates="structures_rel")

    @declared_attr
    def maintenance_rel(self):
        return relationship("Maintenance", back_populates="structures_rel")

    @declared_attr
    def service_clock_rel(self):
        return relationship("ServiceClock", back_populates="structure_rel")

    def get_main_customer_id(self):
        """Get the main customer_id for this structure"""
        if self.customers_rel:
            for customer in self.customers_rel:
                if customer.id != CUSTOMER_ID_DEMO:
                    return customer.id
        return None


class PowerUnit(Base):
    """
    Create a public.power_units table representation
    """

    __tablename__ = "power_units"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    power_unit = Column(DOUBLE_PRECISION, nullable=False, unique=True)
    # This is a calculated/generated column (new to PostgreSQL 12) so we can search it
    # as a string-based association proxy in form AJAX dropdown menu lookups
    power_unit_str = Column(
        TEXT, Computed("CAST(power_unit as VARCHAR(10))"), nullable=False
    )

    power_unit_type_id = Column(INTEGER, ForeignKey("public.power_unit_types.id"))
    power_unit_type_rel = relationship(
        "PowerUnitType", back_populates="power_units_rel"
    )

    # One-to-one relationship between gateway and power_unit (so uselist=False in only this table)
    gateways_rel = relationship("Gw", back_populates="power_units_rel", uselist=False)
    # Define the reverse relationship from PowerUnit to GwInfo
    gateway_info_rel = relationship(
        "GwInfo",
        secondary="public.gw",  # Join through the gateway table
        primaryjoin="PowerUnit.id == Gw.power_unit_id",
        secondaryjoin="Gw.id == GwInfo.gateway_id",
        back_populates="power_unit_rel",
        viewonly=True,  # Make this relationship read-only
        uselist=False,  # Make this a one-to-one relationship
    )
    # records when a user submits a remote control command via the website
    remote_control_rel = relationship("RemoteControl", back_populates="power_unit_rel")

    # Many-to-one relationship
    # More than one structure can be served by a single power unit (e.g. UNOGAS)
    # alerts_rel = relationship('Alert', back_populates='power_unit')
    structures_rel = relationship("Structure", back_populates="power_units_rel")
    # model = association_proxy("structures_rel", "model_type")
    # structure_notes = association_proxy("structures_rel", "notes_1")
    downhole = association_proxy("structures_rel", "downhole")
    surface = association_proxy("structures_rel", "surface")
    # # customers_rel = association_proxy("structures_rel", "customers_rel")

    customers_rel = relationship(
        "Customer",
        secondary=vw_gw_customer_rel,
        back_populates="power_units_rel",
        uselist=True,
        viewonly=True,
        sync_backref=False,
    )
    # We need customer_id so we can find the customer's power units in the admin view
    customer_id = association_proxy("customers_rel", "id")

    # run_mfg_date = Column(TIMESTAMP)
    # DATE is better for formatting in the admin view (don't need mins/seconds)
    run_mfg_date = Column(TIMESTAMP)
    power = Column(
        TEXT
    )  # used to be SMALLINT but Tim needs to add "SS" for "soft start" after the HP
    voltage = Column(INTEGER)
    notes = Column(TEXT)
    # Put "Artificial Intelligence" messages on the card tab on RCOM?
    website_card_msg = Column(BOOLEAN, default=True, nullable=False)

    alerts_rel = relationship("Alert", back_populates="power_units_rel")
    work_orders_rel = relationship(
        "WorkOrder",
        secondary=work_order_power_unit_rel,
        back_populates="power_units_rel",
    )
    modbus_networks_rel = relationship(
        "PowerUnitModbusNetwork", back_populates="power_unit_rel"
    )
    fixed_ip_networks_rel = relationship(
        "PowerUnitFixedIPNetwork", back_populates="power_unit_rel"
    )

    # work_order_parts_rel = relationship(
    #     "WorkOrderPart", back_populates="power_units_rel"
    # )

    maintenance_rel = relationship("Maintenance", back_populates="power_units_rel")
    alerts_sent_wait_okay_rel = relationship(
        "AlertsSentWaitOkay", back_populates="power_unit_rel"
    )

    #######################################################################################
    # Alert settings columns
    apn = Column(VARCHAR)
    heartbeat_enabled = Column(BOOLEAN, default=True)
    online_hb_enabled = Column(BOOLEAN, default=True)
    alerts_edge = Column(BOOLEAN, nullable=False, default=True)

    change_detect_sens = Column(NUMERIC, default=0.8)
    # suction threshold for alerts
    suction = Column(NUMERIC, nullable=False, default=700)
    # discharge threshold for alerts
    discharge = Column(NUMERIC, nullable=False, default=0)
    # SPM threshold for alerts
    spm = Column(NUMERIC, nullable=False, default=0)
    # HT and HT_EGAS threshold for alerts
    hyd_temp = Column(NUMERIC, nullable=False, default=65)
    # STBOXF threshold for alerts
    stboxf = Column(SMALLINT, default=1, nullable=False)

    # Hydraulic oil/filter alerts
    hyd_oil_lvl_thresh = Column(SMALLINT, default=50, nullable=False)
    hyd_filt_life_thresh = Column(SMALLINT, default=50, nullable=False)
    hyd_oil_life_thresh = Column(SMALLINT, default=50, nullable=False)

    wait_time_mins = Column(SMALLINT, default=60, nullable=False)
    wait_time_mins_ol = Column(SMALLINT, default=25, nullable=False)
    wait_time_mins_spm = Column(SMALLINT, default=60)
    wait_time_mins_suction = Column(SMALLINT, nullable=False, default=60)
    wait_time_mins_discharge = Column(SMALLINT, nullable=False, default=240)
    wait_time_mins_hyd_temp = Column(INTEGER, nullable=False, default=60)
    wait_time_mins_stboxf = Column(SMALLINT, default=15, nullable=False)
    # Hydraulic oil alerts
    wait_time_mins_hyd_oil_lvl = Column(INTEGER, default=0, nullable=False)
    wait_time_mins_hyd_filt_life = Column(INTEGER, default=0, nullable=False)
    wait_time_mins_hyd_oil_life = Column(INTEGER, default=0, nullable=False)

    wait_time_mins_chk_mtr_ovld = Column(SMALLINT, default=30, nullable=False)
    wait_time_mins_pwr_fail = Column(SMALLINT, default=30, nullable=False)
    wait_time_mins_soft_start_err = Column(SMALLINT, default=30, nullable=False)
    wait_time_mins_grey_wire_err = Column(SMALLINT, default=30, nullable=False)
    wait_time_mins_ae011 = Column(SMALLINT, default=30, nullable=False)
    #######################################################################################

    def __repr__(self):
        """Return a string representation of the power unit that's easy for customers to understand"""
        repr_str = ""
        if self.power_unit_str:
            repr_str = self.power_unit_str

        downhole = list(self.downhole)
        if downhole and downhole[0]:
            downhole = downhole[0]
            repr_str = f"{repr_str} - {downhole}@" if repr_str else downhole
        else:
            downhole = None

        surface = list(self.surface)
        if surface and surface[0]:
            surface = surface[0]
            if downhole:
                repr_str = f"{repr_str}{surface}"
            else:
                repr_str = f"{repr_str} - {surface}" if repr_str else surface

        return repr_str


class PowerUnitModbusNetwork(Base):
    """
    Each power unit can have multiple Modbus networks (e.g. Aspenleaf XFER 200686)
    """

    __tablename__ = "power_units_modbus_networks"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    ip_address = Column(VARCHAR, nullable=False)
    subnet = Column(VARCHAR(2), nullable=False)
    gateway = Column(VARCHAR, nullable=True)
    # Typically the Modbus connection (eth1) is never the default route for Internet traffic
    never_default = Column(BOOLEAN, nullable=False, default=True)

    power_unit_id = Column(
        INTEGER,
        ForeignKey("public.power_units.id", ondelete="CASCADE"),
        nullable=False,
    )
    power_unit_rel = relationship("PowerUnit", back_populates="modbus_networks_rel")

    def __repr__(self):
        return f"{self.power_unit_rel}: {self.ip_address}/{self.subnet} {self.gateway}"


class PowerUnitFixedIPNetwork(Base):
    """
    Each power unit can have multiple fixed IP networks
    """

    __tablename__ = "power_units_fixed_ip_networks"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    ip_address = Column(VARCHAR, nullable=False)
    subnet = Column(VARCHAR(2), nullable=False)
    gateway = Column(VARCHAR, nullable=True)
    # Typically the non-Modbus connection (eth2) is the default route for Internet traffic,
    # if something like Starlink is used
    never_default = Column(BOOLEAN, nullable=False, default=False)

    power_unit_id = Column(
        INTEGER,
        ForeignKey("public.power_units.id", ondelete="CASCADE"),
        nullable=False,
    )
    power_unit_rel = relationship("PowerUnit", back_populates="fixed_ip_networks_rel")

    def __repr__(self):
        return f"{self.power_unit_rel}: {self.ip_address}/{self.subnet} {self.gateway}"


class SIMCard(Base):
    """
    Create a public.sim_cards table representation
    """

    __tablename__ = "sim_cards"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    sim_card = Column(TEXT, unique=True)
    # SIM Card Alias
    sim_card_num = Column(NUMERIC)

    gateway_id = Column(INTEGER, ForeignKey("public.gw.id"))
    gateways_rel = relationship("Gw", back_populates="sim_card_rel")

    # Need the customer here since customers (e.g. Tundra) may send us batches of SIM cards,
    # with no gateway in which to install them
    customer_id = Column(INTEGER, ForeignKey("public.customers.id"))
    customers_rel = relationship("Customer", back_populates="sim_cards_rel")

    sim_card_activated = Column(BOOLEAN)

    sim_card_phone = Column(TEXT)
    cell_provider = Column(TEXT)
    notes = Column(TEXT)
    location = Column(VARCHAR)

    def __repr__(self):
        # return '<Customer: {}>'.format(self.gateway)
        return f"{self.sim_card}"


class TimeSeries(Base):
    """
    Create a model for the time-series data in the TimescaleDB
    """

    __tablename__ = "time_series"
    # Which database is it in? (ijack or timescale?)
    __bind_key__ = "timescale"
    __table_args__ = {"schema": "public"}

    # id = Column(INTEGER, primary_key=True)

    # This is the MQTT topic gateway, for querying time series data
    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    timestamp_utc = Column(TIMESTAMP, primary_key=True, nullable=False)
    power_unit = Column(VARCHAR, primary_key=True, nullable=False)
    gateway = Column(VARCHAR, nullable=True)
    spm = Column(REAL)
    spm_egas = Column(REAL)
    cgp = Column(REAL)
    dgp = Column(REAL)
    dtp = Column(REAL)
    hpu = Column(REAL)
    hpe = Column(REAL)
    ht = Column(REAL)
    ht_egas = Column(REAL)
    agft = Column(REAL)
    mgp = Column(REAL)
    mgp_sl = Column(REAL)
    agfm = Column(REAL)
    agfn = Column(REAL)
    e3m3_d = Column(REAL)
    m3pd = Column(REAL)
    hyd = Column(BOOLEAN)
    hyd_egas = Column(BOOLEAN)
    warn1 = Column(BOOLEAN)
    warn1_egas = Column(BOOLEAN)
    warn2 = Column(BOOLEAN)
    warn2_egas = Column(BOOLEAN)
    mtr = Column(BOOLEAN)
    mtr_egas = Column(BOOLEAN)
    clr = Column(BOOLEAN)
    clr_egas = Column(BOOLEAN)
    htr = Column(BOOLEAN)
    htr_egas = Column(BOOLEAN)
    aux_egas = Column(BOOLEAN)
    prs = Column(BOOLEAN)
    sbf = Column(BOOLEAN)
    ngp = Column(REAL)
    oh = Column(SMALLINT)
    sh = Column(SMALLINT)
    signal = Column(SMALLINT)

    # TODO: add derate columns from v311 and 312


class TimeSeriesAgg(Base):
    """
    Monthly aggregated time-series data in TimescaleDB, for the performance tab.
    These data get refreshed by the postgresql_scheduler container on the following schedule:

    # min hour dom month dow   command
    # Recalculate aggregated time series records once daily
    11 1 * * * python3 /cron_d/time_series_aggregate_calcs.py
    """

    __tablename__ = "time_series_agg"
    # Which database is it in? (ijack or timescale?)
    __bind_key__ = "timescale"
    __table_args__ = {"schema": "public"}

    power_unit = Column(VARCHAR, primary_key=True, nullable=False)
    month_date = Column(TIMESTAMP, primary_key=True, nullable=False)
    timestamp_utc_modified = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    sample_size = Column(INTEGER)
    stroke_speed_avg = Column(REAL)
    hp_limit = Column(REAL)
    hp_avg = Column(REAL)
    # derate discharge setpoint (mgp)
    mgp_avg = Column(REAL)
    cgp_avg = Column(REAL)  # suction pressure
    dgp_avg = Column(REAL)  # discharge pressure
    # Max discharge temperature before derates (C)
    agf_dis_temp_max_avg = Column(REAL)
    agf_dis_temp_avg = Column(REAL)
    dtp_avg = Column(REAL)
    dtp_max_avg = Column(REAL)


class TimeSeriesRT(Base):
    """
    Create a model for the real-time time series data in the TimescaleDB
    """

    __tablename__ = "time_series_rt"
    # Which database is it in? (ijack or timescale?)
    __bind_key__ = "timescale"
    __table_args__ = {"schema": "public"}

    # id = Column(INTEGER, primary_key=True)

    # This is the MQTT topic gateway, for querying time series data
    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    timestamp_utc = Column(TIMESTAMP, primary_key=True, nullable=False)
    power_unit = Column(VARCHAR, primary_key=True, nullable=False)
    metric = Column(VARCHAR, primary_key=True, nullable=False)
    value = Column(REAL)


class UnitType(Base):
    """
    Create a model for unit types such as XFER, EGAS, UNO, UNOGAS
    """

    __tablename__ = "unit_types"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    unit_type = Column(VARCHAR, unique=True, nullable=False)
    description = Column(TEXT)
    can_show_to_customers = Column(BOOLEAN, default=True, nullable=False)

    # # Many-to-one relationships (structures is not needed since it's implied by the model_type)
    # structures_rel = relationship('Structure', back_populates='unit_type')
    models_rel = relationship("ModelType", back_populates="unit_types_rel")

    structures_rel = relationship(
        "Structure",
        back_populates="unit_types_rel",
        foreign_keys="Structure.unit_type_id",
    )  # many-to-one

    work_orders_rel = relationship(
        "WorkOrder", secondary=work_order_unit_type_rel, back_populates="unit_types_rel"
    )

    application_types_rel = relationship(
        "ApplicationType", back_populates="unit_type_rel"
    )

    def __repr__(self):
        # return '<Model: {}>'.format(self.model)
        return str(self.unit_type)


class Alert(Base):
    """
    Create a model for customer alerts
    """

    __tablename__ = "alerts"
    __table_args__ = (
        UniqueConstraint("user_id", "power_unit_id"),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)
    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive)

    user_id = Column(
        INTEGER, ForeignKey("public.users.id", ondelete="CASCADE"), nullable=False
    )
    users_rel = relationship("User", back_populates="alerts_rel")
    # users_rel_first_name = association_proxy("user", "first_name")
    # user_last_name = association_proxy("user", "last_name")
    # user_email = association_proxy("user", "email")
    # user_phone = association_proxy("user", "phone")
    # users_rel.sms_stop_all = association_proxy("user", "sms_stop_all")
    # # For automatically filtering the customers' alert view by customer ID
    # customer_id = association_proxy("user", "customer_id")

    # Joining a many-to-many relationship via an association table
    # https://stackoverflow.com/a/19736109/3385948
    customers_rel = relationship(
        "Customer",
        secondary=user_customer_rel,
        # Left User model
        primaryjoin=user_customer_rel.c.user_id == id,
        # Right Customer model
        secondaryjoin=user_customer_rel.c.customer_id == Customer.id,
        uselist=False,
        viewonly=True,
    )
    # We need customer_id so the customers can see only their units
    # customer_id = association_proxy("customers_rel", "id")
    # customer = association_proxy("customers_rel", "customer")

    # # Gateway was replaced with power unit as the unit identifier,
    # # so it doesn't change if the gateway is swapped out.
    # gateway_id = Column(
    #     INTEGER,
    #     ForeignKey("public.gw.id"),
    #     # nullable=False
    # )
    # gateways_rel = relationship("Gw", back_populates="alerts_rel")

    power_unit_id = Column(
        INTEGER,
        ForeignKey("public.power_units.id", ondelete="CASCADE"),
        nullable=False,
    )
    power_units_rel = relationship("PowerUnit", back_populates="alerts_rel")

    # # These are view-only since they're from the public.gw table
    # structures = association_proxy("power_unit", "structures")
    # # customers_rel = association_proxy("power_unit", "customers_rel")
    # surface = association_proxy("structures", "surface")
    # downhole = association_proxy("structures", "downhole")

    # Regular alerts
    heartbeat = Column(BOOLEAN, default=True, nullable=False)
    online_hb = Column(BOOLEAN, default=False, nullable=False)
    warn1 = Column(BOOLEAN, default=False, nullable=False)
    warn2 = Column(BOOLEAN, default=False, nullable=False)
    suction = Column(BOOLEAN, default=False, nullable=False)
    discharge = Column(BOOLEAN, default=False, nullable=False)
    mtr = Column(BOOLEAN, default=False, nullable=False)
    # No need to have this SPM alert default=True when the stroking alerts work better.
    # This can be used if you want an alert when the SPM goes below, say, 1 SPM or some threshold
    spm = Column(BOOLEAN, default=False, nullable=False)
    stboxf = Column(BOOLEAN, default=False, nullable=False)
    # HT and HT_EGAS threshold for alerts
    hyd_temp = Column(BOOLEAN, default=False, nullable=False)
    # AI alerts
    wants_card_ml = Column(BOOLEAN, default=False, nullable=False)

    # Change detection alerts
    change_suction = Column(BOOLEAN, default=True, nullable=False)
    change_hyd_temp = Column(BOOLEAN, default=False, nullable=False)
    change_dgp = Column(BOOLEAN, default=True, nullable=False)
    change_hp_delta = Column(BOOLEAN, default=True, nullable=False)

    # Hydraulic oil alerts
    hyd_oil_lvl = Column(BOOLEAN, default=False, nullable=False)
    hyd_filt_life = Column(BOOLEAN, default=False, nullable=False)
    # Hydraulic oil life isn't proven yet, so default it to False
    hyd_oil_life = Column(BOOLEAN, default=False, nullable=False)
    chk_mtr_ovld = Column(BOOLEAN, default=False, nullable=False)
    pwr_fail = Column(BOOLEAN, default=False, nullable=False)
    soft_start_err = Column(BOOLEAN, default=False, nullable=False)
    grey_wire_err = Column(BOOLEAN, default=False, nullable=False)
    ae011 = Column(BOOLEAN, default=False, nullable=False)

    # Alert delivery options
    wants_sms = Column(BOOLEAN, default=True, nullable=False)
    wants_email = Column(BOOLEAN, default=False, nullable=False)
    wants_phone = Column(BOOLEAN, default=False, nullable=False)
    wants_short_sms = Column(BOOLEAN, default=False, nullable=False)
    wants_short_email = Column(BOOLEAN, default=False, nullable=False)
    wants_short_phone = Column(BOOLEAN, default=True, nullable=False)
    wants_whatsapp = Column(BOOLEAN, default=False, nullable=False)

    def __repr__(self):
        # The following don't always work because each gateway might not have a structure
        # if hasattr(self, "power_units_rel"):
        #     if self.power_units_rel and isinstance(self.power_units_rel, list):
        #         # return str(f"{self.user_email} - {self.power_unit} - {self.surface}")
        #         return str(f"{self.power_unit} - {self.power_units_rel[0].surface}")
        return str(f"{self.users_rel} - {self.power_units_rel}")


class AlarmLog(Base):
    """Gateway alarm log"""

    __tablename__ = "alarm_log"
    __table_args__ = {"schema": "public"}

    timestamp_utc_inserted = Column(TIMESTAMP)
    timestamp_local = Column(TIMESTAMP)
    power_unit = Column(VARCHAR, primary_key=True, nullable=False)
    gateway = Column(VARCHAR)
    abbrev = Column(TEXT, primary_key=True, nullable=False)
    value = Column(TEXT, primary_key=True, nullable=False)
    index = Column(SMALLINT, nullable=False, default=0)

    def __repr__(self):
        return f"{self.power_unit} - {self.abbrev} - {self.index} - {self.value}"


class AlarmLogMetric(Base):
    """Gateway alarm log metrics"""

    __tablename__ = "alarm_log_metrics"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    abbrev = Column(VARCHAR, nullable=False)
    index = Column(SMALLINT, nullable=False, default=0)
    name = Column(VARCHAR)
    description = Column(VARCHAR)

    def __repr__(self):
        return f"{self.abbrev} - {self.index} - {self.description}"


class Month(Base):
    """Months of the year"""

    __tablename__ = "months"
    __table_args__ = {"schema": "public"}

    month = Column(SMALLINT, primary_key=True)

    def __repr__(self):
        return str(self.month)


class Day(Base):
    """Days of the year"""

    __tablename__ = "days"
    __table_args__ = {"schema": "public"}

    day = Column(SMALLINT, primary_key=True)

    def __repr__(self):
        return str(self.day)


class DayOfWeek(Base):
    """Days of the week"""

    __tablename__ = "days_of_week"
    __table_args__ = {"schema": "public"}

    id = Column(SMALLINT, primary_key=True)
    name = Column(VARCHAR(3), nullable=False, unique=True)

    def __repr__(self):
        return f"{self.name}"


class Hour(Base):
    """Hours of the day"""

    __tablename__ = "hours"
    __table_args__ = {"schema": "public"}
    # This is an hour-ending actually (1-24)
    hour = Column(SMALLINT, primary_key=True)
    hour_ending = Column(SMALLINT, unique=True)
    id = Column(SMALLINT, Computed("hour"))

    alerts_custom_rel = relationship("AlertCustom", back_populates="hour_end_rel")

    def __repr__(self):
        """Display the hour-beginning, which is more intuitive for users"""
        return f"{self.hour_ending - 1}:00"


class ReportEmailHourly(Base):
    """
    Model for the "Units Down" report emails that come out each morning,
    or every hour, depending on user preferences
    """

    __tablename__ = "report_email_hourly"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    # User doesn't want to see units that are further away than this distance
    max_distance_km = Column(INTEGER, default=2_147_483_647, nullable=False)

    user_id = Column(
        INTEGER, ForeignKey("public.users.id", ondelete="CASCADE"), nullable=False
    )
    users_rel = relationship("User", back_populates="report_email_hourly_rel")

    # Joining a many-to-many relationship via an association table
    # https://stackoverflow.com/a/19736109/3385948
    customers_rel = relationship(
        "Customer",
        secondary=user_customer_rel,
        # Left User model
        primaryjoin=user_customer_rel.c.user_id == user_id,
        # Right Customer model
        secondaryjoin=user_customer_rel.c.customer_id == Customer.id,
        uselist=False,
        viewonly=True,
    )

    # customer = association_proxy("users_rel", "customer")
    # first_name = association_proxy("users_rel", "first_name")
    # last_name = association_proxy("users_rel", "last_name")

    # Many-to-many relationships
    unit_types_rel = relationship(
        "UnitType", secondary=report_email_hourly_unit_types_rel
    )
    model_types_rel = relationship(
        "ModelType", secondary=report_email_hourly_model_types_rel
    )
    # Hours of the day for which the user wants the report
    hours_rel = relationship("Hour", secondary=report_email_hourly_hours_rel)
    days_of_week_rel = relationship(
        "DayOfWeek", secondary=report_email_hourly_days_of_week_rel
    )
    warehouses_rel = relationship(
        "Warehouse", secondary=report_email_hourly_warehouses_rel
    )

    def __repr__(self):
        return (
            f"{self.id} {self.users_rel} {self.unit_types_rel} {self.model_types_rel}"
        )


class ReportEmailDerates(Base):
    """
    Model for the "Units Down" report emails that come out each morning,
    or every hour, depending on user preferences
    """

    __tablename__ = "report_email_derates"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    # User doesn't want to see units that are further away than this distance
    max_distance_km = Column(INTEGER, default=2_147_483_647, nullable=False)
    derate_threshold = Column(SMALLINT, default=20, nullable=False)

    user_id = Column(
        INTEGER, ForeignKey("public.users.id", ondelete="CASCADE"), nullable=False
    )
    users_rel = relationship("User", back_populates="report_email_derates_rel")

    # Joining a many-to-many relationship via an association table
    # https://stackoverflow.com/a/19736109/3385948
    customers_rel = relationship(
        "Customer",
        secondary=user_customer_rel,
        # Left User model
        primaryjoin=user_customer_rel.c.user_id == user_id,
        # Right Customer model
        secondaryjoin=user_customer_rel.c.customer_id == Customer.id,
        uselist=False,
        viewonly=True,
    )

    # customer = association_proxy("users_rel", "customer")
    # first_name = association_proxy("users_rel", "first_name")
    # last_name = association_proxy("users_rel", "last_name")

    # Many-to-many relationships
    unit_types_rel = relationship(
        "UnitType", secondary=report_email_derates_unit_types_rel
    )
    model_types_rel = relationship(
        "ModelType", secondary=report_email_derates_model_types_rel
    )
    # Hours of the day for which the user wants the report
    hours_rel = relationship("Hour", secondary=report_email_derates_hours_rel)
    days_of_week_rel = relationship(
        "DayOfWeek", secondary=report_email_derates_days_of_week_rel
    )
    warehouses_rel = relationship(
        "Warehouse", secondary=report_email_derates_warehouses_rel
    )

    def __repr__(self):
        return (
            f"{self.id} {self.users_rel} {self.unit_types_rel} {self.model_types_rel}"
        )


class ReportEmailOpHours(Base):
    """Report email about units reaching certain thresholds for operating hours"""

    __tablename__ = "report_email_op_hours"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    # User doesn't want to see units that are further away than this distance
    max_distance_km = Column(INTEGER, default=2_147_483_647, nullable=False)

    user_id = Column(
        INTEGER, ForeignKey("public.users.id", ondelete="CASCADE"), nullable=False
    )
    user_rel = relationship("User", back_populates="report_email_op_hours_rel")

    # Joining a many-to-many relationship via an association table
    # https://stackoverflow.com/a/19736109/3385948
    customers_rel = relationship(
        "Customer",
        secondary=user_customer_rel,
        # Left User model
        primaryjoin=user_customer_rel.c.user_id == user_id,
        # Right Customer model
        secondaryjoin=user_customer_rel.c.customer_id == Customer.id,
        uselist=False,
        viewonly=True,
    )

    # Many-to-many relationships
    unit_types_rel = relationship(
        "UnitType", secondary=report_email_op_hours_unit_types_rel
    )
    model_types_rel = relationship(
        "ModelType", secondary=report_email_op_hours_model_types_rel
    )
    # Types of operating hours reports (e.g. 100 hours, 1000 hours, etc)
    types_rel = relationship(
        "ReportEmailOpHoursType", secondary=report_email_op_hours_types_rel
    )
    warehouses_rel = relationship(
        "Warehouse", secondary=report_email_op_hours_warehouses_rel
    )

    def __repr__(self):
        return f"{self.id} {self.user_rel} {self.unit_types_rel} {self.model_types_rel} {self.types_rel}"


class ReportEmailOpHoursType(Base):
    """Types of operating hours reports (e.g. 100 hours, 1000 hours, etc)"""

    __tablename__ = "report_email_op_hours_types"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    name = Column(VARCHAR, nullable=False)
    description = Column(TEXT)

    def __repr__(self):
        return f"{self.name}"


class ReportEmailInventory(Base):
    """
    Model for the "Inventory alerts" report emails that come out each morning,
    or every hour, depending on user preferences
    """

    __tablename__ = "report_email_inventory"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    # User doesn't want to see units that are further away than this distance
    max_distance_km = Column(INTEGER, default=2_147_483_647, nullable=False)
    # derate_threshold = Column(SMALLINT, default=20, nullable=False)

    user_id = Column(
        INTEGER, ForeignKey("public.users.id", ondelete="CASCADE"), nullable=False
    )
    user_rel = relationship("User", back_populates="report_email_inventory_rel")

    # Hours of the day for which the user wants the report
    hours_rel = relationship("Hour", secondary=report_email_inventory_hours_rel)
    # Days of the week for which the user wants the report
    days_of_week_rel = relationship(
        "DayOfWeek", secondary=report_email_inventory_days_of_week_rel
    )
    # Warehouses for which the user wants the report
    warehouses_rel = relationship(
        "Warehouse", secondary=report_email_inventory_warehouses_rel
    )

    def __repr__(self):
        return f"{self.id} {self.user_rel}"


class RemoteControl(Base):
    """
    Create a model for the "remote_control" table, which records when a user
    submits a remote control command via the website
    """

    __tablename__ = "remote_control"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True, nullable=False)
    timestamp_utc = Column(TIMESTAMP, nullable=False)

    aws_thing = Column(VARCHAR, nullable=True)
    dev_test_prd = Column(TEXT, nullable=False)

    power_unit = Column(VARCHAR, nullable=False)
    power_unit_id = Column(INTEGER, ForeignKey("public.power_units.id"), nullable=False)
    power_unit_rel = relationship("PowerUnit", back_populates="remote_control_rel")

    user_id = Column(
        INTEGER, ForeignKey("public.users.id", ondelete="CASCADE"), nullable=False
    )
    users_rel = relationship("User", back_populates="remote_control_rel")

    metric = Column(VARCHAR)
    value_wanted = Column(VARCHAR)
    action = Column(VARCHAR)

    def __repr__(self):
        # return '<Model: {}>'.format(self.model)
        return f"""<RemoteControl(
            user_id={self.user_id},
            metric={self.metric},
            value_wanted={self.value_wanted},
            action={self.action},
            timestamp_utc={self.timestamp_utc},
            aws_thing={self.aws_thing},
            dev_test_prd={self.dev_test_prd})>"""


class Rod(Base):
    """Create a model for the "rods" table"""

    __tablename__ = "rods"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True, nullable=False)

    name = Column(TEXT, nullable=False, unique=True)
    description = Column(TEXT)
    structures_rel = relationship("Structure", back_populates="rod_rel")  # many-to-one

    def __repr__(self):
        # return '<Model: {}>'.format(self.name)
        return f"{self.name}"


class Barrel(Base):
    """Create a model for the "barrels" table"""

    __tablename__ = "barrels"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True, nullable=False)

    name = Column(TEXT, nullable=False, unique=True)
    description = Column(TEXT)
    structures_rel = relationship(
        "Structure", back_populates="barrel_rel"
    )  # many-to-one

    def __repr__(self):
        # return '<Model: {}>'.format(self.name)
        return f"{self.name}"


class ShuttleValve(Base):
    """Create a model for the "shuttle_valves" table"""

    __tablename__ = "shuttle_valves"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True, nullable=False)

    name = Column(TEXT, nullable=False, unique=True)
    description = Column(TEXT)
    structures_rel = relationship(
        "Structure", back_populates="shuttle_valve_rel"
    )  # many-to-one

    def __repr__(self):
        # return '<Model: {}>'.format(self.name)
        return f"{self.name}"


class CheckValve(Base):
    """Create a model for the "check_valves" table"""

    __tablename__ = "check_valves"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True, nullable=False)
    name = Column(TEXT, nullable=False, unique=True)
    description = Column(TEXT)
    structures_rel = relationship(
        "Structure", back_populates="check_valve_rel"
    )  # many-to-one

    def __repr__(self):
        # return '<Model: {}>'.format(self.name)
        return f"{self.name}"


class PackingGland(Base):
    """Create a model for the "packing_glands" table"""

    __tablename__ = "packing_glands"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True, nullable=False)

    name = Column(TEXT, nullable=False, unique=True)
    description = Column(TEXT)
    structures_rel = relationship(
        "Structure", back_populates="packing_gland_rel"
    )  # many-to-one

    def __repr__(self):
        # return '<Model: {}>'.format(self.name)
        return f"{self.name}"


class HydPistonType(Base):
    """Create a model for the "hyd_piston_types" table"""

    __tablename__ = "hyd_piston_types"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True, nullable=False)

    name = Column(VARCHAR, nullable=False, unique=True)
    description = Column(TEXT)
    structures_rel = relationship("Structure", back_populates="hyd_piston_type_rel")

    def __repr__(self):
        # return '<Model: {}>'.format(self.name)
        return f"{self.name}"


class GatewayType(Base):
    """Types of gateways"""

    __tablename__ = "gateway_types"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    name = Column(TEXT, nullable=False, unique=True)
    description = Column(TEXT)

    gateways_rel = relationship("Gw", back_populates="gateway_types_rel")

    def __repr__(self):
        return f"{self.name}"


class StructureByModel(Base):
    """View for seeing how many structures of each type have been made"""

    __tablename__ = "vw_structures_by_model"
    __table_args__ = {
        "schema": "public",
        "info": {
            "view": True,
            "script": r"""
                SELECT row_number() OVER () AS id,
                t2.model,
                max(t1.structure_install_date) AS most_recent_install_date,
                sum(
                    CASE
                        WHEN t1.model_type_id IS NOT NULL THEN 1
                        ELSE 0
                    END) AS total_units
                FROM structures t1
                    LEFT JOIN public.model_types t2 ON t1.model_type_id = t2.id
                    LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id
                WHERE (t2.model <> ALL (ARRAY['SHOP'::text, 'TEST'::text])) AND t4.customer_id IS DISTINCT FROM 21
                GROUP BY t2.model
                ORDER BY t2.model
            """,
        },
    }

    id = Column(INTEGER, primary_key=True)
    model = Column(VARCHAR, nullable=False)
    # DATE is better for formatting in the admin view (don't need mins/seconds)
    # most_recent_install_date = Column(TIMESTAMP)
    most_recent_install_date = Column(DATE)
    total_units = Column(BIGINT)

    def __repr__(self):
        return f"{self.model}"


class MetaDataTbl(Base):
    """Table for holding the colour of cells in Flask Admin views"""

    __tablename__ = "meta_data"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    id_cell = Column(VARCHAR, nullable=False)
    element = Column(VARCHAR, nullable=False)
    color = Column(VARCHAR, nullable=False)

    def __repr__(self):
        return f"{self.id_cell}"


class CompressionPattern(Base):
    """Types of compression card patterns"""

    __tablename__ = "compression_patterns"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    description = Column(TEXT, nullable=False, unique=True)
    # About half of these patterns are alert-worthy and half are not
    # For some reason if we don't set a default here, when testing,
    # the Flask Admin view's form doesn't have an InputRequired or DataRequired validator.
    # Seems like a bug but it's easier just to set a default here.
    send_alert = Column(BOOLEAN, nullable=False, default=False)
    solution = Column(TEXT)
    explanation = Column(TEXT)

    # many-to-one (many cluster IDs for each pattern description)
    images_rel = relationship("CompressionImage", back_populates="description_rel")

    def __repr__(self):
        return f"{self.description}"


class SurfacePattern(Base):
    """Types of surface card patterns"""

    __tablename__ = "surface_patterns"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    description = Column(TEXT, nullable=False, unique=True)
    # About half of these patterns are alert-worthy and half are not
    # For some reason if we don't set a default here, when testing,
    # the Flask Admin view's form doesn't have an InputRequired or DataRequired validator.
    # Seems like a bug but it's easier just to set a default here.
    send_alert = Column(BOOLEAN, nullable=False, default=False)
    solution = Column(TEXT)
    explanation = Column(TEXT)

    # many-to-one (many cluster IDs for each pattern description)
    images_rel = relationship("SurfaceImage", back_populates="description_rel")

    def __repr__(self):
        return f"{self.description}"


class CompressionImage(Base):
    """Compression card images"""

    __tablename__ = "compression_images"
    __table_args__ = (
        UniqueConstraint(
            "cluster_id",
            "ml_version",
            name="compression_images_cluster_id_ml_version_key",
        ),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)
    cluster_id = Column(SMALLINT, nullable=False)
    ml_version = Column(SMALLINT, nullable=False)
    image = Column(BYTEA, nullable=False)
    num_in_cluster = Column(INTEGER)

    # One-to-many relationship
    pattern_id = Column(INTEGER, ForeignKey("public.compression_patterns.id"))
    description_rel = relationship("CompressionPattern", back_populates="images_rel")
    # send_alert = association_proxy("description", "send_alert")
    # solution = association_proxy("description", "solution")
    # explanation = association_proxy("description", "explanation")

    def __repr__(self):
        return f"{self.cluster_id}"


class SurfaceImage(Base):
    """Surface card images"""

    __tablename__ = "surface_images"
    __table_args__ = (
        UniqueConstraint(
            "cluster_id", "ml_version", name="surface_images_cluster_id_ml_version_key"
        ),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)
    cluster_id = Column(SMALLINT, nullable=False)
    ml_version = Column(SMALLINT, nullable=False)
    image = Column(BYTEA, nullable=False)
    num_in_cluster = Column(INTEGER)

    # One-to-many relationship
    pattern_id = Column(INTEGER, ForeignKey("public.surface_patterns.id"))
    description_rel = relationship("SurfacePattern", back_populates="images_rel")
    # send_alert = association_proxy("description", "send_alert")
    # solution = association_proxy("description", "solution")
    # explanation = association_proxy("description", "explanation")

    def __repr__(self):
        return f"{self.cluster_id}"


class CareerFile(Base):
    """Career submission resume file uploads"""

    __tablename__ = "career_files"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    name = Column(TEXT, nullable=False)
    pdf = Column(BYTEA, nullable=False)
    timestamp_inserted_utc = Column(TIMESTAMP, nullable=False, default=utcnow_naive)

    user_id = Column(
        INTEGER, ForeignKey("public.users.id", ondelete="CASCADE"), nullable=False
    )
    # One-to-many (one user, many files)
    users_rel = relationship("User", back_populates="career_files_rel")

    career_applications_rel = relationship(
        "CareerApplicationFile", back_populates="career_file_rel"
    )

    def __repr__(self):
        return f"{self.name}"


class CareerApplication(Base):
    """Career applications"""

    __tablename__ = "career_applications"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    job_type = Column(TEXT, nullable=False)
    message = Column(TEXT)
    timestamp_inserted_utc = Column(TIMESTAMP, nullable=False)

    user_id = Column(
        INTEGER, ForeignKey("public.users.id", ondelete="CASCADE"), nullable=False
    )
    # One-to-many (one user, many applications)
    users_rel = relationship("User", back_populates="career_applications_rel")

    career_files_rel = relationship(
        "CareerApplicationFile", back_populates="career_application_rel"
    )

    def __repr__(self):
        return f"{self.job_type}"


class CareerApplicationFile(Base):
    """
    Many-to-many table for career applications and their files
    (users can apply multiple times)
    """

    __tablename__ = "career_applications_files_rel"
    __table_args__ = (
        UniqueConstraint("career_application_id", "career_file_id"),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)
    career_application_id = Column(
        INTEGER,
        ForeignKey("public.career_applications.id", ondelete="CASCADE"),
        nullable=False,
    )
    career_application_rel = relationship(
        "CareerApplication", back_populates="career_files_rel"
    )

    career_file_id = Column(
        INTEGER,
        ForeignKey("public.career_files.id", ondelete="CASCADE"),
        nullable=False,
    )
    career_file_rel = relationship(
        "CareerFile", back_populates="career_applications_rel"
    )

    def __repr__(self):
        return f"{self.career_application_rel.job_type} - {self.career_file_rel.name}"


class MapAbbrevItem(Base):
    """CAN bus cob ID mapping table"""

    __tablename__ = "map_abbrev_item"
    __table_args__ = (
        UniqueConstraint(
            "abbrev",
            "cob",
            "byte_start_index",
            "bit_start_index",
            name="map_abbrev_item_unique_cob_byte_bit_abbrev",
        ),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)
    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    cob = Column(VARCHAR, nullable=False)
    byte_start_index = Column(SMALLINT)
    bit_start_index = Column(SMALLINT)
    abbrev = Column(VARCHAR)
    plus1_variable = Column(VARCHAR)
    plus1_program = Column(VARCHAR)
    rcom_name = Column(VARCHAR)
    rcom_tab = Column(VARCHAR)
    machine = Column(VARCHAR)
    item = Column(VARCHAR)
    units = Column(VARCHAR)
    description = Column(TEXT)
    num_bytes = Column(SMALLINT)
    data_type = Column(VARCHAR)
    decimals = Column(SMALLINT, default=0, nullable=False)
    signed = Column(BOOLEAN, default=False, nullable=False)
    resolution = Column(VARCHAR)
    offset_ = Column(VARCHAR)
    number_ = Column(VARCHAR)
    interesting = Column(VARCHAR)
    controller_version = Column(VARCHAR)
    color = Column(VARCHAR)
    # Advanced remote control
    control_num = Column(INTEGER)
    # enum is deprecated since it always gets the ENUM from the controller!
    enum = Column(INTEGER)

    modbus_holding_registers_rel = relationship(
        "ModbusHoldingRegister", back_populates="abbrev_rel"
    )

    def __repr__(self):
        return f"{self.abbrev} - {self.item}"


class ModbusHoldingRegister(Base):
    """Mapping the abbrev/metric from the map_abbrev_item table to the holding register and number of registers"""

    __tablename__ = "modbus_holding_registers"
    __table_args__ = (
        UniqueConstraint(
            "address",
            name="modbus_holding_registers_address_key",
        ),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)

    abbrev_id = Column(INTEGER, ForeignKey("public.map_abbrev_item.id"), nullable=False)
    abbrev_rel = relationship(
        "MapAbbrevItem", back_populates="modbus_holding_registers_rel"
    )

    address = Column(SMALLINT, nullable=False, unique=True)
    n_registers = Column(SMALLINT, nullable=False, default=1)
    # This is a calculated field, equal to address + 40,001
    holding_reg_40k = Column(SMALLINT, Computed("address + 40001"), nullable=False)
    in_web_api = Column(BOOLEAN, default=True, nullable=False)
    writable_modbus = Column(BOOLEAN, default=False, nullable=False)
    writable_web_api = Column(BOOLEAN, default=False, nullable=False)
    min_val = Column(REAL, default=0, nullable=False)
    max_val = Column(REAL, default=1000, nullable=False)

    def __repr__(self):
        return f"{self.address} ({self.n_registers})"


class Calculator(Base):
    """XFER calculator"""

    __tablename__ = "calculators"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    name = Column(VARCHAR, nullable=False, unique=True)
    description = Column(TEXT)

    # One-to-many relationship to IJACK model
    model_type_id = Column(INTEGER, ForeignKey("public.model_types.id"))
    model_types_rel = relationship("ModelType", back_populates="calculators_rel")
    # unit_type = association_proxy("model_type", "unit_type")
    # unit_type_id = association_proxy("model_type", "unit_type_id")

    # One-to-many relationship to power unit model
    power_unit_type_id = Column(INTEGER, ForeignKey("public.power_unit_types.id"))
    power_unit_type_rel = relationship(
        "PowerUnitType", back_populates="calculators_rel"
    )

    diameter = Column(FLOAT)
    area = Column(FLOAT)
    stroke = Column(FLOAT)
    max_spm = Column(FLOAT)
    max_delta_p = Column(FLOAT)
    mawp = Column(FLOAT)
    rod_size = Column(FLOAT)
    motor_hp = Column(FLOAT)
    hyds = Column(FLOAT)
    # These are calculated/generated columns (new to PostgreSQL 12)
    max_liquid_m3pd = Column(
        FLOAT, Computed("floor(area * stroke * max_spm * 24 / 1017 / 100) * 100")
    )
    min_liquid_m3pd_10pct = Column(
        FLOAT, Computed("floor(area * stroke * max_spm * 24 / 1017 / 100) * 100 * 0.1")
    )

    # @property
    # def max_liquid_m3pd(self):
    #     return floor(self.area * self.stroke * self.max_spm * 24 / 1017 / 100) * 100

    # @property
    # def min_liquid_m3pd_10pct(self):
    #     return self.max_liquid_m3pd * 0.1

    hyd_size_inch = Column(FLOAT)
    single_loss_cu_inch = Column(FLOAT)
    port_area_sq_inch = Column(FLOAT)
    # Calculated/generated columns (new to PostgreSQL 12)
    # calc1 = Column(FLOAT, Computed("pi() / 4 * diameter * diameter"))
    single_port_gas_level = Column(
        FLOAT,
        Computed(
            "((pi() / 4 * diameter * diameter) - port_area_sq_inch) / (pi() / 4 * diameter * diameter)"
        ),
    )

    # @property
    # def single_port_gas_level(self):
    #     calc1 = pi / 4 * self.diameter * self.diameter
    #     return (calc1 - self.port_area_sq_inch) / calc1

    charge_pump_disp_cc = Column(FLOAT)
    charge_pump_press_psi = Column(FLOAT)
    friction_min_delta_p = Column(FLOAT)
    friction_max_delta_p = Column(FLOAT)

    def __repr__(self):
        return f"{self.name}"


class WebsiteView(Base):
    """Record who visits which IJACK pages, for analysis"""

    __tablename__ = "website_views"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    page = Column(VARCHAR, nullable=False)
    timestamp_utc = Column(TIMESTAMP, nullable=False)

    # This can be nullable because the user may not be logged in
    user_id = Column(
        INTEGER, ForeignKey("public.users.id", ondelete="CASCADE"), nullable=True
    )
    # One-to-many (one user, many applications)
    users_rel = relationship("User", back_populates="website_views_rel")
    # first_name = association_proxy("user", "first_name")
    # last_name = association_proxy("user", "last_name")
    # email = association_proxy("user", "email")
    # phone = association_proxy("user", "phone")
    # customer = association_proxy("user", "customer")
    # # For filtering on customer ID
    # customer_id = association_proxy("user", "customer_id")

    # These columns will be overwritten in an aggregation in the get_query() method
    count_records = Column(INTEGER)

    city = Column(VARCHAR)
    region = Column(VARCHAR)
    timezone = Column(VARCHAR)
    company = Column(VARCHAR)
    country_code = Column(VARCHAR)
    country_name = Column(VARCHAR)
    ip = Column(VARCHAR)
    postal = Column(VARCHAR)
    gps_lat = Column(REAL)
    gps_lon = Column(REAL)

    def __repr__(self):
        return f"{self.page} - {self.users_rel}"


class WebsiteViewMostActive(Base):
    """View for checking most active users by page visited"""

    __tablename__ = "vw_website_most_active_users"
    __table_args__ = {
        "schema": "public",
        "info": {
            "view": True,
            "script": r"""
            SELECT row_number() OVER () AS id,
            t1.page,
            count(*) AS count_,
            t3.customer,
            t2.first_name,
            t2.last_name,
            t2.email,
            t2.phone,
            min(t1.timestamp_utc) AS earliest_date_in_sample,
            t2.customer_id
            FROM public.website_views t1
                LEFT JOIN public.users t2 ON t2.id = t1.user_id
                LEFT JOIN public.customers t3 ON t3.id = t2.customer_id
            WHERE t1.user_id IS NOT NULL AND t1.page::text !~~ '%media%'::text AND t1.page::text !~~ '%protected%'::text AND (t2.customer_id <> ALL (ARRAY[1, 21]))
            GROUP BY t2.first_name, t2.last_name, t2.email, t2.phone, t3.customer, t2.customer_id, t1.page
            ORDER BY t1.page, (count(*)) DESC, t3.customer, t2.first_name, t2.last_name
""",
        },
    }

    id = Column(INTEGER, primary_key=True)
    page = Column(VARCHAR)
    count_ = Column(INTEGER)
    first_name = Column(VARCHAR)
    last_name = Column(VARCHAR)
    email = Column(VARCHAR)
    phone = Column(VARCHAR)
    # DATE is better for formatting in the admin view (don't need mins/seconds)
    # earliest_date_in_sample = Column(TIMESTAMP)
    earliest_date_in_sample = Column(DATE)

    customer = Column(VARCHAR)
    customer_id = Column(INTEGER)
    # customers_rel = relationship("Customer", backref="website_most_active_customers")

    def __repr__(self):
        return f"Page '{self.page}'. Count '{self.count_}'. Customer '{self.customer}'. Name '{self.first_name} {self.last_name}'"


class AlertsSent(Base):
    """Table that stores details when alerts are sent"""

    __tablename__ = "alerts_sent"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    timestamp_utc = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    gateway = Column(VARCHAR)
    power_unit = Column(VARCHAR, nullable=False)
    good1_bad0 = Column(SMALLINT)
    warning_msg = Column(TEXT)
    explanation = Column(TEXT)
    program = Column(VARCHAR)
    function = Column(VARCHAR)

    # We filter on this in sqla_views.py
    dev_test_prd = Column(VARCHAR)

    alerts_sent_users_rel = relationship(
        "AlertsSentUser",
        back_populates="alerts_sent_rel",
        foreign_keys="[AlertsSentUser.alerts_sent_id]",
    )

    def __repr__(self):
        return f"{self.power_unit} {self.timestamp_utc}"


class AlertsSentUser(Base):
    """Users who were alerted"""

    __tablename__ = "alerts_sent_users"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    dev_test_prd = Column(VARCHAR, nullable=False)
    msg_type = Column(VARCHAR)

    twilio_sid = Column(VARCHAR)
    twilio_delivery_status = Column(VARCHAR)
    mailgun_id = Column(VARCHAR)
    mailgun_delivery_status = Column(VARCHAR)

    # Many-to-one relationship (many users, one alerts_sent_maint)
    alerts_sent_id = Column(
        INTEGER, ForeignKey("public.alerts_sent.id"), nullable=False
    )
    alerts_sent_rel = relationship(
        "AlertsSent",
        back_populates="alerts_sent_users_rel",
    )

    user_id = Column(
        INTEGER, ForeignKey("public.users.id", ondelete="CASCADE"), nullable=False
    )
    users_rel = relationship("User", back_populates="alerts_sent_users_rel")
    email_ap = association_proxy("users_rel", "email")
    first_name_ap = association_proxy("users_rel", "first_name")
    last_name_ap = association_proxy("users_rel", "last_name")

    def __repr__(self):
        return f"{self.first_name_ap} {self.last_name_ap} ({self.email_ap})"


class AlertsSentMaint(Base):
    """Table that stores when 6-month maintenance emails are sent"""

    __tablename__ = "alerts_sent_maint"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    timestamp_utc = Column(TIMESTAMP, nullable=False, default=utcnow_naive)

    # customer = Column(VARCHAR)
    # email_type = Column(VARCHAR)
    email_type_id = Column(
        INTEGER,
        ForeignKey("public.alerts_sent_maint_email_types.id"),
        nullable=False,
    )
    alerts_sent_maint_email_types_rel = relationship(
        "AlertsSentMaintEmailType",
        back_populates="alerts_sent_maint_rel",
    )
    email_types_rel_name_proxy = association_proxy(
        "alerts_sent_maint_email_types_rel", "name"
    )

    # We filter on this in sqla_views.py
    dev_test_prd = Column(TEXT)

    customer_id = Column(INTEGER, ForeignKey("public.customers.id"), nullable=False)
    customers_rel = relationship("Customer", back_populates="alerts_sent_maint_rel")
    customers_rel_customer_proxy = association_proxy("customers_rel", "customer")

    alerts_sent_maint_users_rel = relationship(
        "AlertsSentMaintUser",
        back_populates="alerts_sent_maint_rel",
        foreign_keys="[AlertsSentMaintUser.alerts_sent_maint_id]",
    )

    def __repr__(self):
        return f"{self.timestamp_utc} {self.email_types_rel_name_proxy}"


class AlertsSentMaintUser(Base):
    """Users who were emailed when 6-month maintenance emails were sent"""

    __tablename__ = "alerts_sent_maint_users"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    dev_test_prd = Column(CHAR(11))

    alerts_sent_maint_id = Column(
        INTEGER, ForeignKey("public.alerts_sent_maint.id"), nullable=False
    )
    # Many-to-one relationship (many users, one alerts_sent_maint)
    alerts_sent_maint_rel = relationship(
        "AlertsSentMaint",
        back_populates="alerts_sent_maint_users_rel",
    )
    alerts_sent_maint_rel_timestamp_utc_proxy = association_proxy(
        "alerts_sent_maint_rel", "timestamp_utc"
    )
    # alerts_sent_maint_rel_customers_rel = association_proxy(alerts_sent_maint_rel, "customers_rel")

    user_id = Column(
        INTEGER, ForeignKey("public.users.id", ondelete="CASCADE"), nullable=False
    )
    users_rel = relationship("User", back_populates="alerts_sent_maint_users_rel")
    # users_rel_first_name_rel = association_proxy(users_rel, "first_name")
    # users_rel_last_name_rel = association_proxy(users_rel, "last_name")
    # users_rel_email_rel = association_proxy(users_rel, "email")
    # users_rel_phone_rel = association_proxy(users_rel, "phone")

    # NOTE these are from when this table was a view
    # timestamp_utc = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    # customer = Column(VARCHAR)
    # first_name = Column(VARCHAR)
    # last_name = Column(VARCHAR)
    # email = Column(VARCHAR)
    # phone = Column(VARCHAR)

    def __repr__(self):
        return f"id '{self.id}'. alerts_sent_maint_id '{self.alerts_sent_maint_id}'. user_id '{self.user_id}'."


class AlertsSentMaintEmailType(Base):
    """Create a model for the "alerts_sent_maint_email_types" table"""

    __tablename__ = "alerts_sent_maint_email_types"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True, nullable=False)
    name = Column(VARCHAR, nullable=False, unique=True)
    description = Column(VARCHAR)

    alerts_sent_maint_rel = relationship(
        "AlertsSentMaint",
        back_populates="alerts_sent_maint_email_types_rel",
        foreign_keys="[AlertsSentMaint.email_type_id]",
    )

    def __repr__(self):
        return f"{self.name}"


class AlertsSentOther(Base):
    """
    Table that stores when other emails are sent,
    like little alerts that get sent to the admin
    """

    __tablename__ = "alerts_sent_other"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    timestamp_utc_sent = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    alert_type = Column(VARCHAR, nullable=False)
    power_unit_str = Column(VARCHAR)
    aws_thing = Column(VARCHAR)

    def __repr__(self):
        return f"{self.alert_type} {self.timestamp_utc_sent}"


class AlertsSentWaitOkay(Base):
    """
    Table that tracks alerts that are waiting for a condition to return to normal
    before sending an "okay" message
    """

    __tablename__ = "alerts_sent_wait_okay"
    __table_args__ = (
        UniqueConstraint("power_unit_id", "alert_type_id", "is_prod"),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)
    timestamp_updated_utc = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    # metric = Column(VARCHAR)
    wait_okay = Column(BOOLEAN, nullable=False, default=False)
    is_prod = Column(BOOLEAN, nullable=False, default=False)

    power_unit_id = Column(INTEGER, ForeignKey("public.power_units.id"), nullable=False)
    power_unit_rel = relationship(
        "PowerUnit", back_populates="alerts_sent_wait_okay_rel"
    )
    power_unit_str = association_proxy("power_unit_rel", "power_unit_str")

    # Links to alert types
    alert_type_id = Column(INTEGER, ForeignKey("public.alerts_types.id"))
    alert_type_rel = relationship(
        "AlertsType", back_populates="alerts_sent_wait_okay_rel"
    )

    def __repr__(self):
        return f"{self.power_unit_str} {self.alert_type_rel} {self.metric} {self.timestamp_updated_utc}"


class AlertsType(Base):
    """
    Table that stores different types of alerts that can be sent
    """

    __tablename__ = "alerts_types"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    name = Column(VARCHAR, nullable=False)
    description = Column(TEXT)

    # Relationship with alerts_sent_wait_okay
    alerts_sent_wait_okay_rel = relationship(
        "AlertsSentWaitOkay",
        back_populates="alert_type_rel",
        cascade="all, delete-orphan",
    )

    def __repr__(self):
        return f"{self.name}"


class VwProfiler(Base):
    """
    Create a public.vw_profiler "VIEW" representation
    """

    # This is a view, not a table
    __tablename__ = "vw_profiler"
    __table_args__ = {
        "schema": "public",
        "info": {
            "view": True,
            "script": r"""
                    SELECT t1.id,
                    (t2.first_name::text || ' '::text) || t2.last_name::text AS full_name,
                    t3.customer,
                    t1.status_code,
                    t1.timestamp_utc_started,
                    t1.timestamp_utc_ended,
                    t1.elapsed,
                    t1.cpu_start,
                    t1.cpu_end,
                    t1.endpoint_name,
                    t1.referrer,
                    t1.method,
                    t1.args,
                    t1.kwargs,
                    t1.query_string,
                    t1.form,
                    t1.ip,
                    t1.files,
                    t1.path,
                    t1.request_args,
                    t1.scheme,
                    t1.user_agent,
                    t1.body,
                    t1.headers
                    FROM public.profiler_measurements t1
                        LEFT JOIN public.users t2 ON t2.id = t1.user_id
                        LEFT JOIN public.customers t3 ON t3.id = t2.customer_id
                    WHERE t1.elapsed > 3::numeric
                    ORDER BY t1.timestamp_utc_ended DESC
                          """,
        },
    }

    id = Column(INTEGER, primary_key=True)
    full_name = Column(VARCHAR)
    customer = Column(VARCHAR)
    status_code = Column(VARCHAR)
    timestamp_utc_started = Column(TIMESTAMP)
    timestamp_utc_ended = Column(TIMESTAMP)
    elapsed = Column(NUMERIC)
    cpu_start = Column(INTEGER)
    cpu_end = Column(INTEGER)
    endpoint_name = Column(VARCHAR)
    referrer = Column(VARCHAR)
    method = Column(VARCHAR)
    args = Column(VARCHAR)
    kwargs = Column(VARCHAR)
    query_string = Column(VARCHAR)
    form = Column(VARCHAR)
    ip = Column(VARCHAR)
    files = Column(VARCHAR)
    path = Column(VARCHAR)
    request_args = Column(VARCHAR)
    scheme = Column(VARCHAR)
    user_agent = Column(TEXT)
    body = Column(TEXT)
    headers = Column(TEXT)

    def __repr__(self):
        return f"{self.full_name} - {self.customer} - {self.status_code} - {self.timestamp_utc_started} - {self.elapsed} - {self.endpoint_name}"


class VW_Gw_Not_Connected_Dont_Worry_Latest(Base):
    __tablename__ = "vw_gw_not_connected_dont_worry_latest"
    __table_args__ = {
        "schema": "public",
        "info": {
            "view": True,
            "script": r"""
SELECT t2.gateway_id,
    t2.days_since_reported,
    t2.timestamp_utc_last_reported,
    t1.timestamp_utc AS timestamp_utc_worried,
    t1.am_i_worried,
    t1.why_worried,
    t1.operators_contacted
   FROM gw_info t2
     LEFT JOIN ( SELECT a1.id,
            a1.row_num,
            a1.gateway_id,
            a1.am_i_worried,
            a1.timestamp_utc,
            a1.why_worried,
            a1.operators_contacted
           FROM ( SELECT gw_not_connected_dont_worry.id,
                    ( SELECT row_number() OVER (PARTITION BY gw_not_connected_dont_worry.gateway_id ORDER BY gw_not_connected_dont_worry.timestamp_utc DESC) AS row_number) AS row_num,
                    gw_not_connected_dont_worry.gateway_id,
                    gw_not_connected_dont_worry.am_i_worried,
                    gw_not_connected_dont_worry.timestamp_utc,
                    gw_not_connected_dont_worry.notes AS why_worried,
                    gw_not_connected_dont_worry.operators_contacted
                   FROM gw_not_connected_dont_worry) a1
          WHERE a1.row_num < 2
          ORDER BY a1.gateway_id) t1 ON t1.gateway_id = t2.gateway_id
  WHERE t2.timestamp_utc_last_reported < t1.timestamp_utc
  ORDER BY t2.gateway_id, t1.timestamp_utc DESC
            """,
        },
    }
    gateway_id = Column(INTEGER, primary_key=True)


class StructureVw(Base):
    """
    Create a public.vw_structures_joined "VIEW" representation
    """

    # This is a view, not a table
    __tablename__ = "vw_structures_joined"
    __table_args__ = {
        "schema": "public",
        "info": {
            "view": True,
            "script": r"""
            SELECT DISTINCT ON (t1.structure, t4.customer_id) t1.id,
            t1.id AS structure_id,
            t1.structure,
            t1.structure_str,
            t1.structure_slave_id,
            t01.structure AS structure_slave,
            t1.power_unit_id,
            t2.power_unit,
            t2.power_unit_str,
            t2.power_unit_type_id,
            t2.website_card_msg,
            t13.name AS power_unit_type,
            t3.id AS gateway_id,
            t3.gateway,
            t3.aws_thing,
            t3.gateway_type_id,
            t1.has_rcom,
            t1.qb_sale,
            t1.unit_type_id,
            t9.unit_type,
            t1.model_type_id,
            t10.model,
            t10.max_delta_p,
            t10.unit_type_id AS model_unit_type_id,
            t100.unit_type AS model_unit_type,
            t1.model_type_id_slave,
            t11.model AS model_slave,
            t11.unit_type_id AS model_unit_type_id_slave,
            t111.unit_type AS model_unit_type_slave,
            t4.customer_id,
            t5.customer,
                CASE
                    WHEN t61.cust_sub_group_id IS NULL THEN 0
                    ELSE t61.cust_sub_group_id
                END AS cust_sub_group_id,
            t6.name AS cust_sub_group,
            t1.run_mfg_date,
            t1.structure_install_date,
            t1.slave_install_date,
            t1.downhole,
            t1.surface,
            t1.location,
            t1.gps_lat,
            t1.gps_lon,
            t1.notes_1,
            t1.well_license,
            t1.time_zone_id,
            t12.time_zone,
            t2.apn,
            t14.am_i_worried,
            t14.days_since_reported,
            t14.operators_contacted,
            t1.op_months_interval,
            t15.hours AS op_hours,
            t15.suction_range,
            t15.has_slave,
            t15.swv_plc,
	        t15.swv_canpy,
            t1.warehouse_id,
            t16.name AS warehouse_name
            FROM structures t1
                LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id
                LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id
                LEFT JOIN gw t3 ON t3.power_unit_id = t2.id
                LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id
                LEFT JOIN public.customers t5 ON t5.id = t4.customer_id
                LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id
                LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id
                LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id
                LEFT JOIN public.users t8 ON t8.id = t7.user_id
                LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id
                LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id
                LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id
                LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id
                LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id
                LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id
                LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id
                LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id
                LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id
                LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id
            ORDER BY t1.structure
            """,
        },
    }

    id = Column(INTEGER, primary_key=True)
    structure_id = Column(INTEGER)
    structure = Column(DOUBLE_PRECISION)
    structure_str = Column(VARCHAR)
    structure_slave_id = Column(INTEGER)
    structure_slave = Column(DOUBLE_PRECISION)
    power_unit_id = Column(INTEGER)
    power_unit = Column(DOUBLE_PRECISION)
    power_unit_str = Column(VARCHAR)
    power_unit_type_id = Column(INTEGER)
    power_unit_type = Column(VARCHAR)
    website_card_msg = Column(BOOLEAN)
    gateway_id = Column(INTEGER)
    gateway = Column(VARCHAR)
    gateway_type_id = Column(INTEGER)
    has_rcom = Column(BOOLEAN)
    aws_thing = Column(VARCHAR)
    qb_sale = Column(VARCHAR)
    unit_type_id = Column(INTEGER)
    unit_type = Column(VARCHAR)
    model_type_id = Column(INTEGER)
    model = Column(VARCHAR)
    max_delta_p = Column(INTEGER)
    model_unit_type_id = Column(INTEGER)
    model_unit_type = Column(VARCHAR)
    model_type_id_slave = Column(INTEGER)
    model_slave = Column(VARCHAR)
    model_unit_type_id_slave = Column(INTEGER)
    model_unit_type_slave = Column(VARCHAR)
    customer_id = Column(INTEGER)
    customer = Column(VARCHAR)
    cust_sub_group_id = Column(INTEGER)
    cust_sub_group = Column(VARCHAR)
    run_mfg_date = Column(DATE)
    structure_install_date = Column(DATE)
    slave_install_date = Column(DATE)
    downhole = Column(VARCHAR)
    surface = Column(VARCHAR)
    location = Column(VARCHAR)
    gps_lat = Column(DOUBLE_PRECISION)
    gps_lon = Column(DOUBLE_PRECISION)
    notes_1 = Column(TEXT)
    well_license = Column(VARCHAR)
    time_zone_id = Column(INTEGER)
    time_zone = Column(VARCHAR)
    apn = Column(VARCHAR)
    op_hours = Column(REAL)
    op_months_interval = Column(REAL)
    suction_range = Column(SMALLINT)
    has_slave = Column(BOOLEAN)
    swv_plc = Column(VARCHAR)
    swv_canpy = Column(VARCHAR)
    warehouse_id = Column(INTEGER)
    warehouse_name = Column(VARCHAR)

    def __repr__(self):
        pu = remove_unnecessary_decimal(
            view=None, context=None, model=self, name="power_unit"
        )
        try:
            if self.downhole[0] in ("", None):
                return f"{pu} - {get_string_rep(self.surface)}"
            else:
                return f"{pu} - {get_string_rep(self.downhole)} @ {get_string_rep(self.surface)}"
        except Exception:
            return f"{pu} - {get_string_rep(self.downhole)} @ {get_string_rep(self.surface)}"


class StructureVwFiltered(Base):
    """
    Same as StructureVw but with more filters on the view,
    so we don't see IJACK units, demo customer units, units without surface location, etc.
    """

    # This is a view, not a table
    __tablename__ = "vw_structures_joined_filtered"
    __table_args__ = {
        "schema": "public",
        "info": {
            "view": True,
            "script": r"""
            SELECT DISTINCT ON (t1.structure, t4.customer_id) t1.id,
                t1.id AS structure_id,
                t1.structure,
                t1.structure_str,
                t1.structure_slave_id,
                t01.structure AS structure_slave,
                t1.power_unit_id,
                t2.power_unit,
                t2.power_unit_str,
                t2.power_unit_type_id,
                t2.website_card_msg,
                t13.name AS power_unit_type,
                t3.id AS gateway_id,
                t3.gateway,
                t3.aws_thing,
                t3.gateway_type_id,
                t1.has_rcom,
                t1.qb_sale,
                t1.unit_type_id,
                t9.unit_type,
                t1.model_type_id,
                t10.model,
                t10.max_delta_p,
                t10.unit_type_id AS model_unit_type_id,
                t100.unit_type AS model_unit_type,
                t1.model_type_id_slave,
                t11.model AS model_slave,
                t11.unit_type_id AS model_unit_type_id_slave,
                t111.unit_type AS model_unit_type_slave,
                t4.customer_id,
                t5.customer,
                    CASE
                        WHEN t61.cust_sub_group_id IS NULL THEN 0
                        ELSE t61.cust_sub_group_id
                    END AS cust_sub_group_id,
                t6.name AS cust_sub_group,
                t1.run_mfg_date,
                t1.structure_install_date,
                t1.slave_install_date,
                t1.downhole,
                t1.surface,
                t1.location,
                t1.gps_lat,
                t1.gps_lon,
                t1.notes_1,
                t1.well_license,
                t1.time_zone_id,
                t12.time_zone,
                t2.apn,
                t14.am_i_worried,
                t14.days_since_reported,
                t14.operators_contacted,
                t1.op_months_interval,
                t15.hours AS op_hours,
                t15.suction_range,
                t15.has_slave,
                t15.swv_plc,
	            t15.swv_canpy,
                t1.warehouse_id,
                t16.name AS warehouse_name
            FROM structures t1
                LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id
                LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id
                LEFT JOIN gw t3 ON t3.power_unit_id = t2.id
                LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id
                LEFT JOIN public.customers t5 ON t5.id = t4.customer_id
                LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id
                LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id
                LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id
                LEFT JOIN public.users t8 ON t8.id = t7.user_id
                LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id
                LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id
                LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id
                LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id
                LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id
                LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id
                LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id
                LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id
                LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id
                LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id
            WHERE t4.customer_id IS DISTINCT FROM 21 AND t1.structure_install_date IS NOT NULL AND t10.model IS NOT NULL AND (t1.model_type_id <> ALL (ARRAY[63, 41, 23, 37, 38])) AND t1.surface IS NOT NULL AND t4.customer_id IS NOT NULL AND (t4.customer_id <> ALL (ARRAY[1, 2, 3])) AND (t1.unit_type_id = 5 OR t1.unit_type_id <> 5 AND t1.time_zone_id IS NOT NULL)
            ORDER BY t1.structure
            """,
        },
    }

    id = Column(INTEGER, primary_key=True)
    structure_id = Column(INTEGER)
    structure = Column(DOUBLE_PRECISION)
    structure_str = Column(VARCHAR)
    structure_slave_id = Column(INTEGER)
    structure_slave = Column(DOUBLE_PRECISION)
    power_unit_id = Column(INTEGER)
    power_unit = Column(DOUBLE_PRECISION)
    power_unit_str = Column(VARCHAR)
    power_unit_type_id = Column(INTEGER)
    power_unit_type = Column(VARCHAR)
    website_card_msg = Column(BOOLEAN)
    gateway_id = Column(INTEGER)
    gateway = Column(VARCHAR)
    gateway_type_id = Column(INTEGER)
    has_rcom = Column(BOOLEAN)
    aws_thing = Column(VARCHAR)
    qb_sale = Column(VARCHAR)
    unit_type_id = Column(INTEGER)
    unit_type = Column(VARCHAR)
    model_type_id = Column(INTEGER)
    model = Column(VARCHAR)
    max_delta_p = Column(INTEGER)
    model_unit_type_id = Column(INTEGER)
    model_unit_type = Column(VARCHAR)
    model_type_id_slave = Column(INTEGER)
    model_slave = Column(VARCHAR)
    model_unit_type_id_slave = Column(INTEGER)
    model_unit_type_slave = Column(VARCHAR)
    customer_id = Column(INTEGER)
    customer = Column(VARCHAR)
    cust_sub_group_id = Column(INTEGER)
    cust_sub_group = Column(VARCHAR)
    run_mfg_date = Column(DATE)
    structure_install_date = Column(DATE)
    slave_install_date = Column(DATE)
    downhole = Column(VARCHAR)
    surface = Column(VARCHAR)
    location = Column(VARCHAR)
    gps_lat = Column(DOUBLE_PRECISION)
    gps_lon = Column(DOUBLE_PRECISION)
    notes_1 = Column(TEXT)
    well_license = Column(VARCHAR)
    time_zone_id = Column(INTEGER)
    time_zone = Column(VARCHAR)
    apn = Column(VARCHAR)
    op_hours = Column(REAL)
    op_months_interval = Column(REAL)
    suction_range = Column(SMALLINT)
    has_slave = Column(BOOLEAN)
    swv_plc = Column(VARCHAR)
    swv_canpy = Column(VARCHAR)
    warehouse_id = Column(INTEGER)
    warehouse_name = Column(VARCHAR)

    def __repr__(self):
        pu = remove_unnecessary_decimal(
            view=None, context=None, model=self, name="power_unit"
        )
        try:
            if self.downhole[0] in ("", None):
                return f"{pu} - {get_string_rep(self.surface)}"
            else:
                return f"{pu} - {get_string_rep(self.downhole)} @ {get_string_rep(self.surface)}"
        except Exception:
            return f"{pu} - {get_string_rep(self.downhole)} @ {get_string_rep(self.surface)}"


class StructureVwAll(Base):
    """
    All structures without "distinct" so we can see all the duplicates
    """

    # This is a view, not a table
    __tablename__ = "vw_structures_all"
    __table_args__ = {
        "schema": "public",
        "info": {
            "view": True,
            "script": r"""
SELECT t1.id,
    t1.id AS structure_id,
    t1.structure,
    t1.structure_str,
    t1.structure_slave_id,
    t01.structure AS structure_slave,
    t1.power_unit_id,
    t2.power_unit,
    t2.power_unit_str,
    t2.power_unit_type_id,
    t2.website_card_msg,
    t13.name AS power_unit_type,
    t3.id AS gateway_id,
    t3.gateway,
    t3.aws_thing,
    t3.gateway_type_id,
    t1.has_rcom,
    t1.qb_sale,
    t1.unit_type_id,
    t9.unit_type,
    t1.model_type_id,
    t10.model,
    t10.max_delta_p,
    t10.unit_type_id AS model_unit_type_id,
    t100.unit_type AS model_unit_type,
    t1.model_type_id_slave,
    t11.model AS model_slave,
    t11.unit_type_id AS model_unit_type_id_slave,
    t111.unit_type AS model_unit_type_slave,
    t4.customer_id,
    t5.customer,
        CASE
            WHEN t61.cust_sub_group_id IS NULL THEN 0
            ELSE t61.cust_sub_group_id
        END AS cust_sub_group_id,
    t6.name AS cust_sub_group,
    t1.run_mfg_date,
    t1.structure_install_date,
    t1.slave_install_date,
    t1.downhole,
    t1.surface,
    t1.location,
    t1.gps_lat,
    t1.gps_lon,
    t1.notes_1,
    t1.well_license,
    t1.time_zone_id,
    t12.time_zone,
    t2.apn,
    t14.am_i_worried,
    t14.days_since_reported,
    t14.operators_contacted,
    t1.op_months_interval,
    t15.hours AS op_hours,
    t15.suction_range,
    t15.has_slave,
    t15.swv_plc,
    t15.swv_canpy,
    t1.warehouse_id,
    t16.name AS warehouse_name
   FROM structures t1
     LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id
     LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id
     LEFT JOIN gw t3 ON t3.power_unit_id = t2.id
     LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id
     LEFT JOIN public.customers t5 ON t5.id = t4.customer_id
     LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id
     LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id
     LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id
     LEFT JOIN public.users t8 ON t8.id = t7.user_id
     LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id
     LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id
     LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id
     LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id
     LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id
     LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id
     LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id
     LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id
     LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id
     LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id
  ORDER BY t1.structure
                          """,
        },
    }

    id = Column(INTEGER, primary_key=True)
    structure_id = Column(INTEGER)
    structure = Column(DOUBLE_PRECISION)
    structure_str = Column(VARCHAR)
    structure_slave_id = Column(INTEGER)
    structure_slave = Column(DOUBLE_PRECISION)
    power_unit_id = Column(INTEGER)
    power_unit = Column(DOUBLE_PRECISION)
    power_unit_str = Column(VARCHAR)
    power_unit_type_id = Column(INTEGER)
    power_unit_type = Column(VARCHAR)
    website_card_msg = Column(BOOLEAN)
    gateway_id = Column(INTEGER)
    gateway = Column(VARCHAR)
    gateway_type_id = Column(INTEGER)
    has_rcom = Column(BOOLEAN)
    aws_thing = Column(VARCHAR)
    qb_sale = Column(VARCHAR)
    unit_type_id = Column(INTEGER)
    unit_type = Column(VARCHAR)
    model_type_id = Column(INTEGER)
    model = Column(VARCHAR)
    max_delta_p = Column(INTEGER)
    model_unit_type_id = Column(INTEGER)
    model_unit_type = Column(VARCHAR)
    model_type_id_slave = Column(INTEGER)
    model_slave = Column(VARCHAR)
    model_unit_type_id_slave = Column(INTEGER)
    model_unit_type_slave = Column(VARCHAR)
    customer_id = Column(INTEGER)
    customer = Column(VARCHAR)
    cust_sub_group_id = Column(INTEGER)
    cust_sub_group = Column(VARCHAR)
    run_mfg_date = Column(DATE)
    structure_install_date = Column(DATE)
    slave_install_date = Column(DATE)
    downhole = Column(VARCHAR)
    surface = Column(VARCHAR)
    location = Column(VARCHAR)
    gps_lat = Column(DOUBLE_PRECISION)
    gps_lon = Column(DOUBLE_PRECISION)
    notes_1 = Column(TEXT)
    well_license = Column(VARCHAR)
    time_zone_id = Column(INTEGER)
    time_zone = Column(VARCHAR)
    apn = Column(VARCHAR)
    op_hours = Column(REAL)
    op_months_interval = Column(REAL)
    suction_range = Column(SMALLINT)
    has_slave = Column(BOOLEAN)
    swv_plc = Column(VARCHAR)
    swv_canpy = Column(VARCHAR)
    warehouse_id = Column(INTEGER)
    warehouse_name = Column(VARCHAR)

    def __repr__(self):
        pu = remove_unnecessary_decimal(
            view=None, context=None, model=self, name="power_unit"
        )
        try:
            if self.downhole[0] in ("", None):
                return f"{pu} - {get_string_rep(self.surface)}"
            else:
                return f"{pu} - {get_string_rep(self.downhole)} @ {get_string_rep(self.surface)}"
        except Exception:
            return f"{pu} - {get_string_rep(self.downhole)} @ {get_string_rep(self.surface)}"


class StructureVwAllFiltered(Base):
    """
    Same as StructureVwAll but with more filters on the view,
    so we don't see IJACK units, demo customer units, units without surface location, etc.
    """

    # This is a view, not a table
    __tablename__ = "vw_structures_all_filtered"
    __table_args__ = {
        "schema": "public",
        "info": {
            "view": True,
            "script": r"""
SELECT t1.id,
    t1.id AS structure_id,
    t1.structure,
    t1.structure_str,
    t1.structure_slave_id,
    t01.structure AS structure_slave,
    t1.power_unit_id,
    t2.power_unit,
    t2.power_unit_str,
    t2.power_unit_type_id,
    t2.website_card_msg,
    t13.name AS power_unit_type,
    t3.id AS gateway_id,
    t3.gateway,
    t3.aws_thing,
    t3.gateway_type_id,
    t1.has_rcom,
    t1.qb_sale,
    t1.unit_type_id,
    t9.unit_type,
    t1.model_type_id,
    t10.model,
    t10.max_delta_p,
    t10.unit_type_id AS model_unit_type_id,
    t100.unit_type AS model_unit_type,
    t1.model_type_id_slave,
    t11.model AS model_slave,
    t11.unit_type_id AS model_unit_type_id_slave,
    t111.unit_type AS model_unit_type_slave,
    t4.customer_id,
    t5.customer,
        CASE
            WHEN t61.cust_sub_group_id IS NULL THEN 0
            ELSE t61.cust_sub_group_id
        END AS cust_sub_group_id,
    t6.name AS cust_sub_group,
    t1.run_mfg_date,
    t1.structure_install_date,
    t1.slave_install_date,
    t1.downhole,
    t1.surface,
    t1.location,
    t1.gps_lat,
    t1.gps_lon,
    t1.notes_1,
    t1.well_license,
    t1.time_zone_id,
    t12.time_zone,
    t2.apn,
    t14.am_i_worried,
    t14.days_since_reported,
    t14.operators_contacted,
    t1.op_months_interval,
    t15.hours AS op_hours,
    t15.suction_range,
    t15.has_slave,
    t15.swv_plc,
    t15.swv_canpy,
    t1.warehouse_id,
    t16.name AS warehouse_name
   FROM structures t1
     LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id
     LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id
     LEFT JOIN gw t3 ON t3.power_unit_id = t2.id
     LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id
     LEFT JOIN public.customers t5 ON t5.id = t4.customer_id
     LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id
     LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id
     LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id
     LEFT JOIN public.users t8 ON t8.id = t7.user_id
     LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id
     LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id
     LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id
     LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id
     LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id
     LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id
     LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id
     LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id
     LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id
     LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id
  WHERE t4.customer_id IS DISTINCT FROM 21 AND t1.structure_install_date IS NOT NULL AND t10.model IS NOT NULL AND (t1.model_type_id <> ALL (ARRAY[63, 41, 23, 37, 38])) AND t1.surface IS NOT NULL AND t4.customer_id IS NOT NULL AND (t4.customer_id <> ALL (ARRAY[1, 2, 3])) AND (t1.unit_type_id = 5 OR t1.unit_type_id <> 5 AND t1.time_zone_id IS NOT NULL)
  ORDER BY t1.structure
                          """,
        },
    }

    id = Column(INTEGER, primary_key=True)
    structure_id = Column(INTEGER)
    structure = Column(DOUBLE_PRECISION)
    structure_str = Column(VARCHAR)
    structure_slave_id = Column(INTEGER)
    structure_slave = Column(DOUBLE_PRECISION)
    power_unit_id = Column(INTEGER)
    power_unit = Column(DOUBLE_PRECISION)
    power_unit_str = Column(VARCHAR)
    power_unit_type_id = Column(INTEGER)
    power_unit_type = Column(VARCHAR)
    website_card_msg = Column(BOOLEAN)
    gateway_id = Column(INTEGER)
    gateway = Column(VARCHAR)
    gateway_type_id = Column(INTEGER)
    has_rcom = Column(BOOLEAN)
    aws_thing = Column(VARCHAR)
    qb_sale = Column(VARCHAR)
    unit_type_id = Column(INTEGER)
    unit_type = Column(VARCHAR)
    model_type_id = Column(INTEGER)
    model = Column(VARCHAR)
    max_delta_p = Column(INTEGER)
    model_unit_type_id = Column(INTEGER)
    model_unit_type = Column(VARCHAR)
    model_type_id_slave = Column(INTEGER)
    model_slave = Column(VARCHAR)
    model_unit_type_id_slave = Column(INTEGER)
    model_unit_type_slave = Column(VARCHAR)
    customer_id = Column(INTEGER)
    customer = Column(VARCHAR)
    cust_sub_group_id = Column(INTEGER)
    cust_sub_group = Column(VARCHAR)
    run_mfg_date = Column(DATE)
    structure_install_date = Column(DATE)
    slave_install_date = Column(DATE)
    downhole = Column(VARCHAR)
    surface = Column(VARCHAR)
    location = Column(VARCHAR)
    gps_lat = Column(DOUBLE_PRECISION)
    gps_lon = Column(DOUBLE_PRECISION)
    notes_1 = Column(TEXT)
    well_license = Column(VARCHAR)
    time_zone_id = Column(INTEGER)
    time_zone = Column(VARCHAR)
    apn = Column(VARCHAR)
    op_hours = Column(REAL)
    op_months_interval = Column(REAL)
    suction_range = Column(SMALLINT)
    has_slave = Column(BOOLEAN)
    swv_plc = Column(VARCHAR)
    swv_canpy = Column(VARCHAR)
    warehouse_id = Column(INTEGER)
    warehouse_name = Column(VARCHAR)

    def __repr__(self):
        pu = remove_unnecessary_decimal(
            view=None, context=None, model=self, name="power_unit"
        )
        try:
            if self.downhole[0] in ("", None):
                return f"{pu} - {get_string_rep(self.surface)}"
            else:
                return f"{pu} - {get_string_rep(self.downhole)} @ {get_string_rep(self.surface)}"
        except Exception:
            return f"{pu} - {get_string_rep(self.downhole)} @ {get_string_rep(self.surface)}"


class VwHoursBilledByFieldTechByWorkOrder(Base):
    """This is a view, not a table"""

    __tablename__ = "vw_hours_billed_by_field_tech_by_work_order"
    __table_args__ = {
        "schema": "public",
        "info": {
            "view": True,
            "script": r"""
WITH work_orders_parts_cad AS (
         SELECT t1_1.work_order_id,
            t1_1.part_id,
            t2_1.date_service,
            t2_1.service_type_id,
            t2_1.is_warranty,
            t1_1.field_tech_id AS user_id,
            sum(t1_1.quantity) AS quantity_billed,
            sum(
                CASE
                    WHEN t2_1.is_warranty IS TRUE THEN 0::numeric
                    ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per
                END) AS cost_before_tax_cad,
            sum(
                CASE
                    WHEN t2_1.is_warranty IS FALSE THEN 0::numeric
                    ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per
                END) AS warranty_cad
           FROM work_orders_parts t1_1
             JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id
             JOIN currencies fx ON fx.id = t2_1.currency_id
          WHERE (t1_1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2_1.customer_id <> ALL (ARRAY[1, 3]))
          GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, t2_1.is_warranty, t1_1.field_tech_id
        ), service_clock_hours_by_year_month_user_id AS (
         SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out))) AS service_year,
            date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out))) AS service_month,
            t1_1.user_id,
            (t2_1.first_name::text || ' '::text) || t2_1.last_name::text AS name_,
            sum(EXTRACT(epoch FROM t1_1.total_hours_worked) / 3600::numeric) AS hours_worked_clock
           FROM service_clock t1_1
             JOIN public.users t2_1 ON t2_1.id = t1_1.user_id
          WHERE t1_1.total_hours_worked IS NOT NULL
          GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))), t1_1.user_id, t2_1.first_name, t2_1.last_name
          ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))) DESC, t1_1.user_id, t2_1.first_name, t2_1.last_name
        )
 SELECT t2.id AS work_order_id,
    date_part('year'::text, t2.date_service) AS service_year,
    date_part('month'::text, t2.date_service) AS service_month,
    t2.is_warranty,
    (t3.first_name::text || ' '::text) || t3.last_name::text AS name_,
    t1.user_id,
    t2.date_service,
    cust.customer,
    avg(sc.hours_worked_clock) AS monthly_hours_worked_clock,
    sum(t1.quantity_billed) AS quantity_hours_billed,
    sum(t1.warranty_cad) AS sales_warranty,
    sum(t1.cost_before_tax_cad) AS sales_total,
    sum(
        CASE
            WHEN t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878]) THEN t1.cost_before_tax_cad
            ELSE 0::numeric
        END) AS sales_labour,
    sum(
        CASE
            WHEN t1.part_id <> ALL (ARRAY[5865, 874, 881, 52644, 880, 879, 878]) THEN t1.cost_before_tax_cad
            ELSE 0::numeric
        END) AS sales_parts,
    sum(
        CASE
            WHEN t5.id = 1 THEN t1.cost_before_tax_cad
            ELSE 0::numeric
        END) AS type_new_installs,
    sum(
        CASE
            WHEN t5.id = 4 THEN t1.cost_before_tax_cad
            ELSE 0::numeric
        END) AS type_sales,
    sum(
        CASE
            WHEN t5.id = 2 THEN t1.cost_before_tax_cad
            ELSE 0::numeric
        END) AS type_repairs,
    sum(
        CASE
            WHEN t5.id = 3 THEN t1.cost_before_tax_cad
            ELSE 0::numeric
        END) AS type_parts,
    sum(
        CASE
            WHEN t5.id = 5 THEN t1.cost_before_tax_cad
            ELSE 0::numeric
        END) AS type_prevent_maint,
    sum(
        CASE
            WHEN t5.id = 7 THEN t1.cost_before_tax_cad
            ELSE 0::numeric
        END) AS type_rentals
   FROM work_orders_parts_cad t1
     JOIN work_orders t2 ON t2.id = t1.work_order_id
     JOIN public.customers cust ON cust.id = t2.customer_id
     JOIN public.users t3 ON t3.id = t1.user_id
     JOIN parts t4 ON t4.id = t1.part_id
     JOIN service_types t5 ON t5.id = t2.service_type_id
     LEFT JOIN service_clock_hours_by_year_month_user_id sc ON sc.service_year = date_part('year'::text, t2.date_service) AND sc.service_month = date_part('month'::text, t2.date_service) AND sc.user_id = t1.user_id
  WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3]))
  GROUP BY t2.id, t2.date_service, t2.is_warranty, cust.customer, t1.user_id, ((t3.first_name::text || ' '::text) || t3.last_name::text), (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service))
  ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('month'::text, t2.date_service)) DESC, t2.is_warranty, ((t3.first_name::text || ' '::text) || t3.last_name::text), t2.date_service DESC
""",
        },
    }

    work_order_id = Column(
        INTEGER,
        ForeignKey("public.work_orders.id", ondelete="CASCADE"),
        primary_key=True,
    )
    work_order_rel = relationship(
        "WorkOrder", back_populates="hours_billed_by_field_tech_rel"
    )

    service_year = Column(INTEGER)
    service_month = Column(INTEGER)
    is_warranty = Column(BOOLEAN)
    name_ = Column(VARCHAR)
    user_id = Column(INTEGER)
    date_service = Column(DATE)
    customer = Column(VARCHAR)
    monthly_hours_worked_clock = Column(REAL)
    quantity_hours_billed = Column(REAL)


class HoursBilledByFieldTechMonthlyEfficiency(Base):
    """This is a view, not a table"""

    __tablename__ = "vw_hours_billed_by_field_tech_monthly_efficiency"
    __table_args__ = {
        "schema": "public",
        "info": {
            "view": True,
            "script": r"""
WITH work_orders_parts_cad AS (
         SELECT t1.work_order_id,
            t1.part_id,
            t2.date_service,
            t2.service_type_id,
            t1.field_tech_id AS user_id,
            sum(t1.quantity) AS quantity_billed,
            sum(
                CASE
                    WHEN t2.is_warranty IS TRUE THEN 0::numeric
                    ELSE t1.cost_before_tax * fx.fx_rate_cad_per
                END) AS cost_before_tax_cad,
            sum(
                CASE
                    WHEN t2.is_warranty IS FALSE THEN 0::numeric
                    ELSE t1.cost_before_tax * fx.fx_rate_cad_per
                END) AS warranty_cad
           FROM work_orders_parts t1
             JOIN work_orders t2 ON t2.id = t1.work_order_id
             JOIN currencies fx ON fx.id = t2.currency_id
          WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3]))
          GROUP BY t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, t1.field_tech_id
        ), service_clock_hours_by_year_month_user_id AS (
         SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_year,
            date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_month,
            t1.user_id,
            (t2.first_name::text || ' '::text) || t2.last_name::text AS name_,
            sum(EXTRACT(epoch FROM t1.total_hours_worked) / 3600::numeric) AS hours_worked_clock
           FROM service_clock t1
             JOIN public.users t2 ON t2.id = t1.user_id
          WHERE t1.total_hours_worked IS NOT NULL
          GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name
          ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, t1.user_id, t2.first_name, t2.last_name
        )
 SELECT row_number() OVER (ORDER BY service_year, service_month, full_name, user_id) AS id,
    service_year,
    service_month,
    full_name,
    user_id,
        CASE
            WHEN monthly_hours_worked_clock = 0::numeric THEN 0::numeric
            ELSE quantity_hours_billed / monthly_hours_worked_clock
        END AS billed_per_hour_worked,
    quantity_hours_billed,
    monthly_hours_worked_clock
   FROM ( SELECT date_part('year'::text, t2.date_service) AS service_year,
            date_part('month'::text, t2.date_service) AS service_month,
            (t3.first_name::text || ' '::text) || t3.last_name::text AS full_name,
            t1.user_id,
            avg(sc.hours_worked_clock) AS monthly_hours_worked_clock,
            sum(t1.quantity_billed) AS quantity_hours_billed
           FROM work_orders_parts_cad t1
             JOIN work_orders t2 ON t2.id = t1.work_order_id
             JOIN public.customers t7 ON t7.id = t2.customer_id
             JOIN public.users t3 ON t3.id = t1.user_id
             JOIN parts t4 ON t4.id = t1.part_id
             JOIN service_types t5 ON t5.id = t2.service_type_id
             LEFT JOIN service_clock_hours_by_year_month_user_id sc ON sc.service_year = date_part('year'::text, t2.date_service) AND sc.service_month = date_part('month'::text, t2.date_service) AND sc.user_id = t1.user_id
          WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND t2.customer_id <> 1
          GROUP BY t1.user_id, ((t3.first_name::text || ' '::text) || t3.last_name::text), (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service))) a1
  ORDER BY service_year DESC, service_month DESC, full_name
""",
        },
    }

    id = Column(INTEGER, primary_key=True)
    service_year = Column(INTEGER)
    service_month = Column(INTEGER)
    full_name = Column(VARCHAR)
    user_id = Column(INTEGER)
    billed_per_hour_worked = Column(REAL)
    quantity_hours_billed = Column(REAL)
    monthly_hours_worked_clock = Column(REAL)


class HoursBilledMonthlyEfficiency(Base):
    """This is a view, not a table"""

    __tablename__ = "vw_hours_billed_monthly_efficiency"
    __table_args__ = {
        "schema": "public",
        "info": {
            "view": True,
            "script": r"""
                WITH work_orders_parts_cad AS (
                        SELECT t1.work_order_id,
                            t1.part_id,
                            t2.date_service,
                            t2.service_type_id,
                            sum(t1.quantity) AS quantity_billed,
                            sum(
                                CASE
                                    WHEN t2.is_warranty IS TRUE THEN 0::numeric
                                    ELSE t1.cost_before_tax * fx.fx_rate_cad_per
                                END) AS cost_before_tax_cad,
                            sum(
                                CASE
                                    WHEN t2.is_warranty IS FALSE THEN 0::numeric
                                    ELSE t1.cost_before_tax * fx.fx_rate_cad_per
                                END) AS warranty_cad
                        FROM work_orders_parts t1
                            JOIN work_orders t2 ON t2.id = t1.work_order_id
                            JOIN currencies fx ON fx.id = t2.currency_id
                        WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3]))
                        GROUP BY t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id
                        ), service_clock_hours_by_year_month_user_id AS (
                        SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_year,
                            date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_month,
                            sum(EXTRACT(epoch FROM t1.total_hours_worked) / 3600::numeric) AS hours_worked_clock
                        FROM service_clock t1
                        WHERE t1.total_hours_worked IS NOT NULL
                        GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))))
                        ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC
                        )
                SELECT row_number() OVER (ORDER BY service_year, service_month) AS id,
                    service_year,
                    service_month,
                        CASE
                            WHEN monthly_hours_worked_clock = 0::numeric THEN 0::numeric
                            ELSE quantity_hours_billed / monthly_hours_worked_clock
                        END AS billed_per_hour_worked,
                    quantity_hours_billed,
                    monthly_hours_worked_clock
                FROM ( SELECT date_part('year'::text, t2.date_service) AS service_year,
                            date_part('month'::text, t2.date_service) AS service_month,
                            avg(sc.hours_worked_clock) AS monthly_hours_worked_clock,
                            sum(t1.quantity_billed) AS quantity_hours_billed
                        FROM work_orders_parts_cad t1
                            JOIN work_orders t2 ON t2.id = t1.work_order_id
                            JOIN public.customers t7 ON t7.id = t2.customer_id
                            JOIN parts t4 ON t4.id = t1.part_id
                            JOIN service_types t5 ON t5.id = t2.service_type_id
                            LEFT JOIN service_clock_hours_by_year_month_user_id sc ON sc.service_year = date_part('year'::text, t2.date_service) AND sc.service_month = date_part('month'::text, t2.date_service)
                        WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND t2.customer_id <> 1
                        GROUP BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service))) a1
                ORDER BY service_year DESC, service_month DESC
            """,
        },
    }

    id = Column(INTEGER, primary_key=True)
    service_year = Column(INTEGER)
    service_month = Column(INTEGER)
    billed_per_hour_worked = Column(REAL)
    quantity_hours_billed = Column(REAL)
    monthly_hours_worked_clock = Column(REAL)


class ImageField(Base):
    """
    Create a table into which we can upload images
    """

    __tablename__ = "images"
    __table_args__ = {
        "schema": "public",
    }

    id = Column(INTEGER, primary_key=True)
    name = Column(String(60), nullable=False)
    description = Column(TEXT)
    format = Column(String(10))
    image = Column(BYTEA, nullable=False)

    def __repr__(self) -> str:
        return self.name


class AlertCustom(Base):
    """
    Create a recurring custom alert for a customer/months/days/hour/users/units
    """

    __tablename__ = "alerts_custom"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    subject = Column(TEXT, nullable=False)
    body = Column(TEXT, nullable=False)
    want_sms = Column(BOOLEAN, default=False)
    want_email = Column(BOOLEAN, default=True)

    # One-to-many relationships
    # Just one customer per custom alert
    customer_id = Column(INTEGER, ForeignKey("public.customers.id"), nullable=False)
    customers_rel = relationship("Customer", back_populates="alerts_custom_rel")

    # Just one hour per custom alert
    hour_ending = Column(
        SMALLINT,
        CheckConstraint("hour_ending >= 1"),
        CheckConstraint("hour_ending <= 24"),
        ForeignKey("public.hours.hour_ending"),
        nullable=False,
        default=8,
    )
    hour_end_rel = relationship("Hour", back_populates="alerts_custom_rel")

    # Just one time zone per custom alert
    time_zone_id = Column(
        INTEGER,
        ForeignKey("public.time_zones.id"),
        default=TIME_ZONE_ID_AMERICA_REGINA,
        nullable=False,
    )
    time_zones_rel = relationship("TimeZone", back_populates="alerts_custom_rel")

    # Many-to-many relationships
    # Many users
    users_rel = relationship(
        "User",
        secondary=alerts_custom_user_rel,
        back_populates="alerts_custom_rel",
    )
    # Many structures
    structures_rel = relationship(
        "Structure",
        secondary=alerts_custom_structure_rel,
        back_populates="alerts_custom_rel",
    )

    # Months of the year for which the user wants the report
    months_rel = relationship("Month", secondary=alerts_custom_months_rel)

    # Days of the month for which the user wants the report
    days_rel = relationship("Day", secondary=alerts_custom_days_rel)

    # Images to attach to the alert email
    images_rel = relationship("ImageField", secondary=alerts_custom_images_rel)


class ServiceClock(Base):
    """
    Service employees clock in and out
    """

    __tablename__ = "service_clock"
    __table_args__ = (
        # I changed this so we must have either a structure_id or a warehouse_id, or both
        CheckConstraint(
            "((structure_id IS NULL AND warehouse_id IS NOT NULL) OR "
            + "(structure_id IS NOT NULL AND warehouse_id IS NULL) OR "
            # + "(structure_id IS NULL AND warehouse_id IS NULL))"
            + "(structure_id IS NOT NULL AND warehouse_id IS NOT NULL))"
        ),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)
    is_billable = Column(BOOLEAN, default=True, nullable=False)
    is_travel = Column(BOOLEAN, default=False, nullable=False)

    user_id = Column(
        INTEGER, ForeignKey("public.users.id", ondelete="CASCADE"), nullable=False
    )
    employee_rel = relationship("User", back_populates="service_clock_rel")

    structure_id = Column(INTEGER, ForeignKey("public.structures.id"), nullable=True)
    structure_rel = relationship("Structure", back_populates="service_clock_rel")

    warehouse_id = Column(INTEGER, ForeignKey("public.warehouses.id"), nullable=True)
    warehouse_rel = relationship("Warehouse", back_populates="service_clock_rel")

    timestamp_utc_in = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    timestamp_utc_out = Column(TIMESTAMP)
    # Calculated field
    total_hours_worked = Column(
        INTERVAL, Computed("timestamp_utc_out - timestamp_utc_in")
    )

    time_zone_in_id = Column(
        INTEGER, ForeignKey("public.time_zones.id"), nullable=False
    )
    time_zone_in_rel = relationship(
        "TimeZone",
        foreign_keys=[time_zone_in_id],
        back_populates="service_clock_in_rel",
    )

    time_zone_out_id = Column(
        INTEGER, ForeignKey("public.time_zones.id"), nullable=False
    )
    time_zone_out_rel = relationship(
        "TimeZone",
        foreign_keys=[time_zone_out_id],
        back_populates="service_clock_out_rel",
    )

    gps_lat_in = Column(DOUBLE_PRECISION)
    gps_lat_out = Column(DOUBLE_PRECISION)

    gps_lon_in = Column(DOUBLE_PRECISION)
    gps_lon_out = Column(DOUBLE_PRECISION)

    notes = Column(TEXT)


class VwServiceClockHoursDaily(Base):
    """
    View, not table, for seeing service clock hours daily
    """

    __tablename__ = "vw_service_clock_hours_daily"
    __table_args__ = {
        "schema": "public",
        "info": {
            "view": True,
            "script": r"""
SELECT row_number() OVER (ORDER BY a1.year_, a1.month_, a1.day_, a1.name_) AS id,
    a1.year_,
    a1.month_,
    a1.day_,
    a1.user_id,
    a1.name_,
    a1.days_worked,
    a1.time_records,
    a1.hours_worked,
    a2.total_hours,
    a2.service_hours,
    a2.travel_hours
   FROM ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_,
            date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS month_,
            date_part('day'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS day_,
            t1.user_id,
            (t2.first_name::text || ' '::text) || t2.last_name::text AS name_,
            date_part('epoch'::text, sum(t1.total_hours_worked)) / 3600::double precision AS hours_worked,
            date_part('epoch'::text, sum(t1.total_hours_worked)) / 86400::double precision AS days_worked,
            string_agg(concat(to_char(timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_in)), 'MM-DD HH24:MI'::text), ' - ', to_char(timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)), 'MM-DD HH24:MI'::text)), ', '::text ORDER BY t1.timestamp_utc_out) AS time_records
           FROM service_clock t1
             JOIN public.users t2 ON t2.id = t1.user_id
          WHERE t1.total_hours_worked IS NOT NULL
          GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('day'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1
     LEFT JOIN ( SELECT work_orders.user_id,
            date_part('year'::text, work_orders.date_service) AS year_,
            date_part('month'::text, work_orders.date_service) AS month_,
            date_part('day'::text, work_orders.date_service) AS day_,
            sum(work_orders.travel_hours) AS travel_hours,
            sum(work_orders.service_hours) AS service_hours,
            sum(work_orders.total_hours) AS total_hours
           FROM ( SELECT t1.date_service,
                        CASE
                            WHEN t2.user_id IS NULL THEN t1.creator_id
                            ELSE t2.user_id
                        END AS user_id,
                        CASE
                            WHEN t3.n_users IS NULL THEN 1::bigint
                            ELSE t3.n_users
                        END::numeric AS travel_hours,
                        CASE
                            WHEN t3.n_users IS NULL THEN 1::bigint
                            ELSE t3.n_users
                        END::numeric AS service_hours,
                        CASE
                            WHEN t3.n_users IS NULL THEN 1::bigint
                            ELSE t3.n_users
                        END::numeric AS total_hours
                   FROM work_orders t1
                     LEFT JOIN work_order_user_rel t2 ON t1.id = t2.work_order_id
                     LEFT JOIN ( SELECT work_order_user_rel.work_order_id,
                            count(DISTINCT work_order_user_rel.user_id) AS n_users
                           FROM work_order_user_rel
                          GROUP BY work_order_user_rel.work_order_id) t3 ON t1.id = t3.work_order_id) work_orders
          GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service)), (date_part('month'::text, work_orders.date_service)), (date_part('day'::text, work_orders.date_service)), (work_orders.date_service::timestamp without time zone)) a2 ON a1.year_ = a2.year_ AND a1.month_ = a2.month_ AND a1.day_ = a2.day_ AND a1.user_id = a2.user_id
  ORDER BY a1.year_, a1.month_, a1.day_, a1.user_id
""",
        },
    }

    id = Column(INTEGER, primary_key=True)
    year_ = Column(DOUBLE_PRECISION)
    month_ = Column(DOUBLE_PRECISION)
    day_ = Column(DOUBLE_PRECISION)
    user_id = Column(INTEGER)
    name_ = Column(VARCHAR)
    hours_worked = Column(DOUBLE_PRECISION)
    days_worked = Column(DOUBLE_PRECISION)
    time_records = Column(TEXT)
    # travel_hours = Column(NUMERIC)
    # service_hours = Column(NUMERIC)
    # total_hours = Column(NUMERIC)


class VwServiceClockHoursMonthly(Base):
    """
    View, not table, for seeing service clock hours monthly
    """

    __tablename__ = "vw_service_clock_hours_monthly"
    __table_args__ = {
        "schema": "public",
        "info": {
            "view": True,
            "script": r"""
SELECT row_number() OVER (ORDER BY a1.year_, a1.month_, a1.name_) AS id,
    a1.year_,
    a1.month_,
    a1.user_id,
    a1.name_,
    a1.days_worked,
    a1.hours_worked,
    a2.total_hours,
    a2.service_hours,
    a2.travel_hours
   FROM ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_,
            date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS month_,
            t1.user_id,
            (t2.first_name::text || ' '::text) || t2.last_name::text AS name_,
            date_part('epoch'::text, sum(t1.total_hours_worked)) / 3600::double precision AS hours_worked,
            date_part('epoch'::text, sum(t1.total_hours_worked)) / 86400::double precision AS days_worked
           FROM service_clock t1
             JOIN public.users t2 ON t2.id = t1.user_id
          WHERE t1.total_hours_worked IS NOT NULL
          GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1
     LEFT JOIN ( SELECT work_orders.user_id,
            date_part('year'::text, work_orders.date_service) AS year_,
            date_part('month'::text, work_orders.date_service) AS month_,
            sum(work_orders.travel_hours) AS travel_hours,
            sum(work_orders.service_hours) AS service_hours,
            sum(work_orders.total_hours) AS total_hours
           FROM ( SELECT t1.date_service,
                        CASE
                            WHEN t2.user_id IS NULL THEN t1.creator_id
                            ELSE t2.user_id
                        END AS user_id,
                        CASE
                            WHEN t3.n_users IS NULL THEN 1::bigint
                            ELSE t3.n_users
                        END::numeric AS travel_hours,
                        CASE
                            WHEN t3.n_users IS NULL THEN 1::bigint
                            ELSE t3.n_users
                        END::numeric AS service_hours,
                        CASE
                            WHEN t3.n_users IS NULL THEN 1::bigint
                            ELSE t3.n_users
                        END::numeric AS total_hours
                   FROM work_orders t1
                     LEFT JOIN work_order_user_rel t2 ON t1.id = t2.work_order_id
                     LEFT JOIN ( SELECT work_order_user_rel.work_order_id,
                            count(DISTINCT work_order_user_rel.user_id) AS n_users
                           FROM work_order_user_rel
                          GROUP BY work_order_user_rel.work_order_id) t3 ON t1.id = t3.work_order_id) work_orders
          GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service)), (date_part('month'::text, work_orders.date_service))) a2 ON a1.year_ = a2.year_ AND a1.month_ = a2.month_ AND a1.user_id = a2.user_id
  ORDER BY a1.year_, a1.month_, a1.user_id
                               """,
        },
    }

    id = Column(INTEGER, primary_key=True)
    year_ = Column(DOUBLE_PRECISION)
    month_ = Column(DOUBLE_PRECISION)
    user_id = Column(INTEGER)
    name_ = Column(VARCHAR)
    hours_worked = Column(DOUBLE_PRECISION)
    days_worked = Column(DOUBLE_PRECISION)
    # travel_hours = Column(NUMERIC)
    # service_hours = Column(NUMERIC)
    # total_hours = Column(NUMERIC)


class VwServiceClockHoursYearly(Base):
    """
    View, not table, for seeing service clock hours yearly
    """

    __tablename__ = "vw_service_clock_hours_yearly"
    __table_args__ = {
        "schema": "public",
        "info": {
            "view": True,
            "script": r"""
                              SELECT row_number() OVER (ORDER BY a1.year_, a1.name_) AS id,
    a1.year_,
    a1.user_id,
    a1.name_,
    a1.days_worked,
    a1.hours_worked,
    a2.total_hours,
    a2.service_hours,
    a2.travel_hours
   FROM ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_,
            t1.user_id,
            (t2.first_name::text || ' '::text) || t2.last_name::text AS name_,
            date_part('epoch'::text, sum(t1.total_hours_worked)) / 3600::double precision AS hours_worked,
            date_part('epoch'::text, sum(t1.total_hours_worked)) / 86400::double precision AS days_worked
           FROM service_clock t1
             JOIN public.users t2 ON t2.id = t1.user_id
          WHERE t1.total_hours_worked IS NOT NULL
          GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1
     LEFT JOIN ( SELECT work_orders.user_id,
            date_part('year'::text, work_orders.date_service) AS year_,
            sum(work_orders.travel_hours) AS travel_hours,
            sum(work_orders.service_hours) AS service_hours,
            sum(work_orders.total_hours) AS total_hours
           FROM ( SELECT t1.date_service,
                        CASE
                            WHEN t2.user_id IS NULL THEN t1.creator_id
                            ELSE t2.user_id
                        END AS user_id,
                        CASE
                            WHEN t3.n_users IS NULL THEN 1::bigint
                            ELSE t3.n_users
                        END::numeric AS travel_hours,
                        CASE
                            WHEN t3.n_users IS NULL THEN 1::bigint
                            ELSE t3.n_users
                        END::numeric AS service_hours,
                        CASE
                            WHEN t3.n_users IS NULL THEN 1::bigint
                            ELSE t3.n_users
                        END::numeric AS total_hours
                   FROM work_orders t1
                     LEFT JOIN work_order_user_rel t2 ON t1.id = t2.work_order_id
                     LEFT JOIN ( SELECT work_order_user_rel.work_order_id,
                            count(DISTINCT work_order_user_rel.user_id) AS n_users
                           FROM work_order_user_rel
                          GROUP BY work_order_user_rel.work_order_id) t3 ON t1.id = t3.work_order_id) work_orders
          GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service))) a2 ON a1.year_ = a2.year_ AND a1.user_id = a2.user_id
  ORDER BY a1.year_, a1.user_id
                              """,
        },
    }

    id = Column(INTEGER, primary_key=True)
    year_ = Column(DOUBLE_PRECISION)
    user_id = Column(INTEGER)
    name_ = Column(VARCHAR)
    hours_worked = Column(DOUBLE_PRECISION)
    days_worked = Column(DOUBLE_PRECISION)
    # travel_hours = Column(NUMERIC)
    # service_hours = Column(NUMERIC)
    # total_hours = Column(NUMERIC)


class ContactForm(Base):
    """
    When a user submits a contact form from https://myijack.com/contact/
    """

    __tablename__ = "contact_form"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive)

    user_id = Column(INTEGER, ForeignKey("public.users.id"), nullable=True)
    user_rel = relationship("User", back_populates="contact_form_rel")

    first_name = Column(VARCHAR, nullable=True)
    last_name = Column(VARCHAR, nullable=True)
    email = Column(VARCHAR, nullable=True)
    phone = Column(VARCHAR, nullable=True)
    message = Column(TEXT, nullable=True)

    def __repr__(self):
        return f"{self.first_name} {self.last_name} ({self.email})"


class ApplicationType(Base):
    """
    For filling out an application for an IJACK product
    EGAS unit types have multiple applications (e.g. gas compressor or casing gas)
    """

    __tablename__ = "application_types"
    __table_args__ = {
        "schema": "public",
    }

    id = Column(INTEGER, primary_key=True)
    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive)

    name = Column(VARCHAR, nullable=False)
    description = Column(TEXT)

    unit_type_id = Column(
        INTEGER,
        ForeignKey("public.unit_types.id"),
        nullable=False,
    )
    unit_type_rel = relationship("UnitType", back_populates="application_types_rel")

    # One-to-many (one application type, many applications)
    applications_rel = relationship(
        "Application", back_populates="application_types_rel"
    )

    def __repr__(self):
        return f"{self.name}"


class Application(Base):
    """For filling out an application for an IJACK product"""

    __tablename__ = "applications"
    __table_args__ = {
        "schema": "public",
    }

    id = Column(INTEGER, primary_key=True)
    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive)

    application_type_id = Column(
        INTEGER,
        ForeignKey("public.application_types.id"),
        nullable=False,
    )
    application_types_rel = relationship(
        "ApplicationType", back_populates="applications_rel"
    )

    user_id = Column(
        INTEGER, ForeignKey("public.users.id"), nullable=True, default=None
    )
    user_rel = relationship("User", back_populates="applications_rel")

    customer_id = Column(
        INTEGER,
        ForeignKey("public.customers.id"),
        nullable=True,
    )
    customer_rel = relationship("Customer", back_populates="applications_rel")

    company_name = Column(VARCHAR, default=None, nullable=False)
    street = Column(TEXT)
    city = Column(VARCHAR, default=None, nullable=True)

    province_id = Column(
        INTEGER,
        ForeignKey("public.provinces.id"),
        default=None,
        nullable=True,
    )
    province_rel = relationship("Province", back_populates="applications_rel")

    country_id = Column(
        INTEGER,
        ForeignKey("public.countries.id"),
        default=None,
        nullable=True,
    )
    countries_rel = relationship("Country", back_populates="applications_rel")

    contact_name = Column(VARCHAR, nullable=False)
    contact_phone = Column(CHAR(15), nullable=False)
    contact_email = Column(VARCHAR, nullable=False)

    install_location = Column(VARCHAR)
    field_contact_name = Column(VARCHAR)
    field_contact_phone = Column(CHAR(15))
    field_contact_email = Column(VARCHAR)

    project_objective = Column(TEXT)
    current_process_equipment = Column(TEXT)
    current_process_issues = Column(TEXT)

    when_need_equipment = Column(TEXT)

    # XFER-specific stuff
    emulsion_coming_from = Column(TEXT)
    emulsion_discharging_into = Column(TEXT)

    # VRU-specific stuff
    vapour_coming_off_storage = Column(BOOLEAN, default=False, nullable=False)
    vapour_coming_off_vr_tanks = Column(BOOLEAN, default=False, nullable=False)
    vapour_coming_off_other = Column(TEXT)
    vapour_discharging_into = Column(TEXT)
    tank_pressure_rating = Column(NUMERIC)
    tank_pressure_desired = Column(NUMERIC)

    # EGAS stuff
    casing_count = Column(INTEGER)
    discharging_into_emulsion1_or_gas_line2 = Column(SMALLINT)
    flowline_pressure_on_test = Column(NUMERIC)
    artificial_lift_system = Column(TEXT)
    separators_installed = Column(BOOLEAN, default=False, nullable=False)
    compressor_b4_separators = Column(BOOLEAN, default=False, nullable=False)

    # DGAS stuff
    well_pumped_off_status = Column(SMALLINT)
    pump_fillage_pct = Column(NUMERIC)
    formation_pressure = Column(NUMERIC)
    pump_make = Column(VARCHAR)
    pump_model = Column(VARCHAR)
    pump_speed_spm = Column(NUMERIC)
    pump_stroke_length = Column(NUMERIC)
    pump_rod_load = Column(NUMERIC)
    pump_long_stroke = Column(BOOLEAN)
    pump_set_on_cement1_piles2 = Column(SMALLINT)
    pump_base_height = Column(NUMERIC)
    pump_num_rails = Column(SMALLINT)
    pump_num_tiedowns = Column(SMALLINT)

    # Corrosive elements
    use_ppm1_percent2 = Column(SMALLINT, default=1, nullable=False)
    h2s = Column(NUMERIC, default=0, nullable=False)
    co2 = Column(NUMERIC, default=0, nullable=False)
    salinity = Column(NUMERIC, default=0, nullable=False)

    # VRU's use Oz/inch^2
    use_psi1_kpa2_ozsqinch3 = Column(SMALLINT, default=1, nullable=False)
    inlet_pressure_current = Column(NUMERIC, default=0, nullable=False)
    inlet_pressure_desired = Column(NUMERIC, default=0, nullable=False)
    discharge_pressure = Column(NUMERIC, default=0, nullable=False)

    # Use e3m3/d or Mcf/d
    use_e3m3d1_mcfd2 = Column(SMALLINT, default=1, nullable=False)
    expected_gas_volume = Column(NUMERIC, default=0, nullable=False)
    expected_water_volume = Column(NUMERIC, default=0, nullable=False)
    expected_oil_volume = Column(NUMERIC, default=0, nullable=False)

    # Use C or F
    use_celsius1_fahrenheit2 = Column(SMALLINT, default=1, nullable=False)
    inlet_temp = Column(NUMERIC, default=0, nullable=False)
    max_discharge_flowline_temp = Column(NUMERIC, default=0, nullable=False)
    max_ambient_temp = Column(NUMERIC, default=0, nullable=False)

    # Booleans for sand or parafin
    sand = Column(Boolean, default=False, nullable=False)
    frac_sand = Column(Boolean, default=False, nullable=False)
    parafin = Column(Boolean, default=False, nullable=False)
    other_solids = Column(TEXT)

    pipeline_diameter_inlet_inches = Column(NUMERIC, default=0, nullable=False)
    pipeline_diameter_discharge_inches = Column(NUMERIC, default=0, nullable=False)

    # Power on site
    power_available = Column(Boolean, default=True, nullable=False)
    power_voltage = Column(NUMERIC, default=0, nullable=False)
    power_phase = Column(NUMERIC, default=0, nullable=False)
    power_amps = Column(NUMERIC, default=0, nullable=False)
    power_grid = Column(Boolean, default=False, nullable=False)
    power_generator = Column(Boolean, default=False, nullable=False)

    # Fuel sources available
    fuel_diesel = Column(Boolean, default=False, nullable=False)
    fuel_natural_gas = Column(Boolean, default=False, nullable=False)
    fuel_propane = Column(Boolean, default=False, nullable=False)

    # Cellular signal strength
    cell_signal_good = Column(Boolean, default=True, nullable=False)

    # Special metals/coatings needed
    special_metals_needed = Column(Boolean, default=False, nullable=False)

    # Special requirements/additional info
    special_requirements = Column(TEXT)

    application_upload_files_rel = relationship(
        "ApplicationUploadFile",
        back_populates="applications_rel",
        cascade="all, delete-orphan",
    )


class ApplicationUploadFile(Base):
    """For files uploaded from applications submitted"""

    __tablename__ = "application_upload_files"
    __table_args__ = {
        "schema": "public",
    }

    id = Column(INTEGER, primary_key=True)
    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive)

    application_id = Column(
        INTEGER,
        ForeignKey("public.applications.id"),
        nullable=False,
    )
    applications_rel = relationship(
        "Application", back_populates="application_upload_files_rel"
    )

    file_name = Column(VARCHAR, nullable=False)
    file_type = Column(VARCHAR, nullable=False)
    file_bytes = Column(BYTEA, nullable=False)

    def __repr__(self):
        return str(self.file_name)


class DiagnosticMetric(Base):
    """Diagnostic metrics (numbers) from cobs 0x730 and 0x731 on the gateway CAN bus"""

    __tablename__ = "diagnostic_metrics"
    # Which database is it in? (ijack or timescale?)
    __bind_key__ = "timescale"
    __table_args__ = {
        "schema": "public",
    }

    id = Column(INTEGER, primary_key=True)
    timestamp_utc_modified = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    diag_num = Column(SMALLINT, nullable=False)
    name = Column(VARCHAR, nullable=False)
    description = Column(TEXT)
    decimals = Column(SMALLINT, nullable=False)
    units = Column(VARCHAR)
    color = Column(VARCHAR)


class TimeSeriesDiagnostic(Base):
    """Diagnostic time-series data in the TimescaleDB database"""

    __tablename__ = "time_series_diagnostic"
    # Which database is it in? (ijack or timescale?)
    __bind_key__ = "timescale"
    __table_args__ = {"schema": "public"}

    power_unit_str = Column(VARCHAR, primary_key=True)
    timestamp_utc = Column(TIMESTAMP, primary_key=True)
    timestamp_utc_modified = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    diag_num = Column(SMALLINT, primary_key=True)
    is_main = Column(BOOLEAN, primary_key=True)
    value = Column(NUMERIC, nullable=False)


class Diagnostic(Base):
    """Diagnostic card data from cobs 0x730 and 0x731 on the gateway CAN bus"""

    __tablename__ = "diagnostic"
    # Which database is it in? (ijack or timescale?)
    __bind_key__ = "timescale"
    __table_args__ = {"schema": "public"}

    power_unit_str = Column(VARCHAR, primary_key=True)
    timestamp_utc = Column(TIMESTAMP, primary_key=True)
    timestamp_utc_modified = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    is_main = Column(BOOLEAN, primary_key=True)
    msg_type = Column(SMALLINT, primary_key=True)
    diag_num = Column(SMALLINT, primary_key=True)
    value = Column(NUMERIC, nullable=False)


class Measurement(Base):
    """Flask profiler measurements"""

    __tablename__ = "profiler_measurements"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)

    timestamp_utc_started = Column(TIMESTAMP)
    timestamp_utc_ended = Column(TIMESTAMP)
    # Computed field
    elapsed_seconds = Column(
        NUMERIC,
        Computed("EXTRACT(EPOCH FROM timestamp_utc_ended - timestamp_utc_started)"),
    )

    started_at = Column(NUMERIC)
    ended_at = Column(NUMERIC)
    elapsed = Column(NUMERIC)
    cpu_start = Column(INTEGER)
    cpu_end = Column(INTEGER)

    endpoint_name = Column(VARCHAR)
    endpoint = Column(VARCHAR)
    url = Column(VARCHAR)
    args = Column(TEXT)
    kwargs = Column(TEXT)
    request_args = Column(TEXT)
    form = Column(TEXT)
    body = Column(TEXT)
    headers = Column(TEXT)
    ip = Column(VARCHAR)
    method = Column(VARCHAR)
    status_code = Column(SMALLINT)
    user_agent = Column(VARCHAR)
    path = Column(VARCHAR)
    referrer = Column(VARCHAR)
    query_string = Column(VARCHAR)
    scheme = Column(VARCHAR)
    files = Column(TEXT)

    user_id = Column(
        INTEGER, ForeignKey("public.users.id", ondelete="CASCADE"), nullable=True
    )
    user_rel = relationship("User", back_populates="profiler_measurements_rel")

    def __repr__(self):
        return f"Measurement {self.id} {self.endpoint_name} {self.method} {self.status_code} {self.elapsed}"


class ErrorLog(Base):
    """
    Model for tracking application errors and exceptions.
    Used to record, monitor, and resolve issues encountered by users.
    """

    __tablename__ = "error_logs"
    __table_args__ = {"schema": "public"}  # Using the same schema as your other tables

    # Primary key
    id = Column(INTEGER, primary_key=True)
    # When the error occurred
    timestamp_utc = Column(TIMESTAMP, default=utcnow_naive, nullable=False)

    # User information (optional - some errors might occur for anonymous users)
    user_id = Column(
        INTEGER, ForeignKey("public.users.id", ondelete="SET NULL"), nullable=True
    )
    user_rel = relationship("User", back_populates="error_logs", foreign_keys=[user_id])

    # Error details
    error_type = Column(String(255), nullable=True)  # Type of exception
    error_message = Column(TEXT, nullable=True)  # Error message
    error_traceback = Column(TEXT, nullable=True)  # Full stack trace

    # Request context
    request_method = Column(String(10), nullable=True)  # GET, POST, etc.
    request_url = Column(String(2048), nullable=True)  # URL that was accessed
    request_params = Column(JSONB, nullable=True)  # Query parameters
    request_body = Column(JSONB, nullable=True)  # POST/PUT body data
    request_headers = Column(JSONB, nullable=True)  # HTTP headers

    # Client information
    user_agent = Column(String(512), nullable=True)  # Browser info
    client_ip = Column(String(45), nullable=True)  # IPv4/IPv6 address

    # Application context
    app_version = Column(String(50), nullable=True)  # Version of your app
    environment = Column(String(20), nullable=True)  # dev/staging/production
    status_code = Column(INTEGER, nullable=True)  # HTTP status code

    # Resolution tracking
    resolved = Column(BOOLEAN, default=False, nullable=False)
    resolution_notes = Column(TEXT, nullable=True)
    resolved_by_user_id = Column(
        INTEGER, ForeignKey("public.users.id", ondelete="SET NULL"), nullable=True
    )
    resolved_by_user = relationship(
        "User", back_populates="resolved_errors", foreign_keys=[resolved_by_user_id]
    )
    resolved_at_utc = Column(TIMESTAMP, nullable=True)

    def __repr__(self) -> str:
        """String representation of the error log entry"""
        return f"<ErrorLog(id={self.id}, type={self.error_type}, timestamp={self.timestamp_utc})>"

    def mark_as_resolved(self, user_id: int, notes: Optional[str] = None) -> None:
        """Mark this error as resolved with optional resolution notes"""
        self.resolved = True
        self.resolved_by_user_id = user_id
        self.resolved_at_utc = utcnow_naive()
        if notes:
            self.resolution_notes = notes


class Calorie(Base):
    """Calories consumed or burned by a user"""

    __tablename__ = "calories"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    timestamp_utc = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    calories = Column(INTEGER, nullable=False)

    user_id = Column(
        INTEGER, ForeignKey("public.users.id", ondelete="CASCADE"), nullable=False
    )
    user_rel = relationship("User", back_populates="calories_rel")

    calorie_type_id = Column(
        INTEGER, ForeignKey("public.calorie_types.id"), nullable=False
    )
    calorie_type_rel = relationship("CalorieType", back_populates="calories_rel")

    def __add__(self, other):
        """For adding two Calorie objects together"""
        if isinstance(other, Calorie):
            return self.calories + other.calories
        return NotImplemented

    def __sub__(self, other):
        """For subtracting two Calorie objects"""
        if isinstance(other, Calorie):
            return self.calories - other.calories
        return NotImplemented

    def __repr__(self):
        return f"Calories {self.calories} {self.calorie_type_rel} {self.timestamp_utc}"


class CalorieType(Base):
    """Calorie types (consumed or burned)"""

    __tablename__ = "calorie_types"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    name = Column(VARCHAR, nullable=False)
    description = Column(TEXT)

    # One-to-many (one calorie type, many calories)
    calories_rel = relationship("Calorie", back_populates="calorie_type_rel")

    def __repr__(self):
        return f"{self.name}"


class UserChartPreference(Base):
    """
    Store user preferences for chart metrics to remember their selections.

    This model associates a user with their selected chart metrics for different
    chart categories and chart types (main or real-time).
    """

    __tablename__ = "user_chart_preferences"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    user_id = Column(
        INTEGER,
        ForeignKey("public.users.id", ondelete="CASCADE"),
        nullable=False,
    )
    # The user associated with these preferences
    user_rel = relationship("User", back_populates="chart_preferences_rel")

    # # The structure this preference is for (can be null for global preferences)
    # structure_id = Column(
    #     INTEGER,
    #     ForeignKey("public.structures.id", ondelete="CASCADE"),
    #     nullable=True,
    # )
    # structure_rel = relationship("Structure", back_populates="chart_preferences_rel")

    # Identifies which chart these preferences are for: 'main' or 'real_time'
    chart_type = Column(VARCHAR(20), nullable=False)

    # Identifies the category of metrics: e.g., 'pressure', 'speed', etc.
    chart_category = Column(VARCHAR(50), nullable=False)

    # Stores the selected metric names as a JSON array
    selected_metrics = Column(JSON, nullable=False)

    def __repr__(self):
        """String representation of the model."""
        return f"<UserChartPreference: User {self.user_id}, Chart {self.chart_type}, Category {self.chart_category}>"


class UserChartToggle(Base):
    """
    Store user display preferences for charts that apply globally.

    This model stores display preferences that are independent of chart category
    and chart type, such as unit preferences (KPA vs PSI, CF vs m3, etc.)
    """

    __tablename__ = "user_chart_toggles"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    user_id = Column(
        INTEGER,
        ForeignKey("public.users.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,  # Each user can have only one set of toggles
    )
    # The user associated with these toggles
    user_rel = relationship("User", back_populates="chart_toggles_rel")

    # Whether to use KPA instead of PSI
    use_kpa = Column(Boolean, nullable=False, default=False)

    # Other display preferences
    use_cf = Column(Boolean, nullable=False, default=False)
    use_barrels = Column(Boolean, nullable=False, default=False)
    use_fahrenheit = Column(Boolean, nullable=False, default=False)
    use_oz_per_inch2_for_suction = Column(Boolean, nullable=False, default=False)

    def __repr__(self):
        """String representation of the model."""
        return f"<UserChartToggle: User {self.user_id}>"
